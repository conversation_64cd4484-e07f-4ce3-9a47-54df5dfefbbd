.navbar {
  background-color: #1e3a5f;
  padding: 0.5rem 1rem;
  transition: background-color 0.3s ease, opacity 0.3s ease;
  opacity: 0.95;
}

.navbar:hover {
  opacity: 1;
  background-color: #1e3a5f;
}

/* Brand text or fallback */
.brand-text {
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
  letter-spacing: 0.5px;
  margin-right: 1rem;
  display: flex;
  align-items: center;
}

.brand-logo {
  height: 32px;
  margin-right: 8px;
  display: inline-block;
}

/* Nav Links */
.nav-link,
.logout-button,
.navbar-brand {
  transition: color 0.3s ease, transform 0.3s ease;
}

.nav-link {
  color: white !important;
  margin: 0 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-link:hover {
  color: #e0e0e0 !important;
  transform: scale(1.03);
}

/* Tools Icon (gear) */
.nav-icon {
  margin-right: 4px;
}

/* Logout Button */
.logout-button {
  background-color: #dc3545 !important;
  border: none !important;
  padding: 0.375rem 0.75rem;
  margin-left: 0.5rem;
  transition: background-color 0.3s ease;
  opacity: 0.9;
}

.logout-button:hover {
  background-color: #c82333 !important;
  opacity: 1;
  transform: scale(1.03);
}

.navbar-end {
  display: flex;
  align-items: center;
  gap: 12px;
}

.navbar-data-indicator {
  font-size: 11px;
}

/* Collapse control */
.navbar .navbar-collapse .ms-auto {
  display: flex !important;
  align-items: center;
  justify-content: flex-end;
  width: auto;
}

.navbar .navbar-collapse .ms-auto .nav-link {
  padding: 0.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-collapse.show .ms-auto,
.navbar-collapse.collapsing .ms-auto {
  display: flex !important;
  width: 100%;
  justify-content: flex-end;
}

/* Discovery Pill */
.discover-pill {
  border-radius: 50px;
  font-size: 12px;
  padding: 4px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid #ffffff33;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.discover-pill:hover {
  background-color: #ffffff22;
  color: #e0e0e0;
}

/* Time Slider Wrapper */
.time-slider-wrapper {
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  align-items: center;
}

@media (max-width: 992px) {
  .time-slider-wrapper {
    position: absolute;
    top: 60px;
    right: 10px;
    z-index: 1060;
  }
}

/* Flex spacer */
.flex-grow-1 {
  flex-grow: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .navbar .ms-auto {
    margin-top: 0.5rem;
  }
}
