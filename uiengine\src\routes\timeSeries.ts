import express from 'express';
import { TimeSeriesService, TimeSeriesQuery } from '../services/timeSeriesService';

const router = express.Router();

/**
 * @swagger
 * /api/time-series/analyze:
 *   post:
 *     summary: Perform time-series analysis on temporal data
 *     description: Analyzes temporal satellite data to generate various products including NDVI trends, VCI analysis, anomaly detection, and seasonal patterns
 *     tags:
 *       - Time Series Analysis
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - geometry
 *               - startDate
 *               - endDate
 *               - analysisType
 *               - layers
 *             properties:
 *               geometry:
 *                 type: object
 *                 description: GeoJSON Polygon defining the area of interest
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for analysis (YYYY-MM-DD)
 *                 example: "2024-01-01"
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for analysis (YYYY-MM-DD)
 *                 example: "2024-12-31"
 *               analysisType:
 *                 type: string
 *                 enum: [static_mosaic, ndvi_trend, vci_analysis, anomaly_detection, seasonal_patterns]
 *                 description: Type of temporal analysis to perform
 *                 example: "ndvi_trend"
 *               layers:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of temporal layers to analyze
 *                 example: ["geonode:sentinel_ndvi", "geonode:landsat_mosaic"]
 *               aoiId:
 *                 type: integer
 *                 description: Optional AOI ID for reference
 *     responses:
 *       200:
 *         description: Time-series analysis completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     analysisId:
 *                       type: string
 *                       description: Unique identifier for this analysis
 *                     analysisType:
 *                       type: string
 *                       description: Type of analysis performed
 *                     dateRange:
 *                       type: object
 *                       properties:
 *                         startDate:
 *                           type: string
 *                         endDate:
 *                           type: string
 *                     products:
 *                       type: object
 *                       description: Analysis products (varies by analysis type)
 *                     metadata:
 *                       type: object
 *                       properties:
 *                         processingTime:
 *                           type: number
 *                           description: Processing time in milliseconds
 *                         dataPoints:
 *                           type: number
 *                           description: Number of data points analyzed
 *                         qualityScore:
 *                           type: number
 *                           description: Quality score (0-100)
 *                         generatedAt:
 *                           type: string
 *                           format: date-time
 *                 message:
 *                   type: string
 *                   example: "Time-series analysis completed successfully"
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid analysis type"
 *                 details:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Time-series analysis failed"
 */
router.post('/analyze', async (req, res) => {
  try {
    console.log('🕒 Time-series analysis request received:', {
      analysisType: req.body.analysisType,
      dateRange: `${req.body.startDate} to ${req.body.endDate}`,
      layers: req.body.layers?.length || 0
    });

    // Validate required fields
    const { geometry, startDate, endDate, analysisType, layers } = req.body;
    
    if (!geometry || !startDate || !endDate || !analysisType || !layers) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        details: ['geometry, startDate, endDate, analysisType, and layers are required']
      });
    }

    // Validate analysis type
    const validTypes = ['static_mosaic', 'ndvi_trend', 'vci_analysis', 'anomaly_detection', 'seasonal_patterns'];
    if (!validTypes.includes(analysisType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid analysis type',
        details: [`Analysis type must be one of: ${validTypes.join(', ')}`]
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid date format',
        details: ['Dates must be in YYYY-MM-DD format']
      });
    }

    // Validate date range
    if (new Date(startDate) >= new Date(endDate)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid date range',
        details: ['Start date must be before end date']
      });
    }

    // Validate geometry
    if (!geometry.type || geometry.type !== 'Polygon' || !geometry.coordinates) {
      return res.status(400).json({
        success: false,
        error: 'Invalid geometry',
        details: ['Geometry must be a valid GeoJSON Polygon']
      });
    }

    // Validate layers array
    if (!Array.isArray(layers) || layers.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid layers',
        details: ['Layers must be a non-empty array of layer names']
      });
    }

    // Build query object
    const query: TimeSeriesQuery = {
      geometry,
      startDate,
      endDate,
      analysisType,
      layers,
      aoiId: req.body.aoiId
    };

    // Perform analysis
    const result = await TimeSeriesService.performTimeSeriesAnalysis(query);

    res.json({
      success: true,
      data: result,
      message: 'Time-series analysis completed successfully'
    });

  } catch (error) {
    console.error('❌ Time-series analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Time-series analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @swagger
 * /api/time-series/types:
 *   get:
 *     summary: Get available time-series analysis types
 *     description: Returns a list of available analysis types with descriptions and expected outputs
 *     tags:
 *       - Time Series Analysis
 *     responses:
 *       200:
 *         description: List of available analysis types
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       expectedOutputs:
 *                         type: array
 *                         items:
 *                           type: string
 *                       processingTime:
 *                         type: string
 *                       dataRequirements:
 *                         type: string
 */
router.get('/types', async (req, res) => {
  try {
    const analysisTypes = [
      {
        type: 'static_mosaic',
        name: 'Static Mosaic',
        description: 'Generates a single composite image for the selected date range. This is the traditional approach that provides a snapshot view.',
        expectedOutputs: ['Composite WMS layer', 'Download URL', 'Coverage statistics'],
        processingTime: 'Fast (< 30 seconds)',
        dataRequirements: 'Any temporal layer',
        useCase: 'Quick visualization of conditions over a time period'
      },
      {
        type: 'ndvi_trend',
        name: 'NDVI Trend Analysis',
        description: 'Analyzes vegetation health trends over time using NDVI (Normalized Difference Vegetation Index). Provides statistical trend analysis and time-series visualization.',
        expectedOutputs: ['Trend statistics', 'Time-series chart', 'Stacked layers', 'Statistical summary'],
        processingTime: 'Medium (1-3 minutes)',
        dataRequirements: 'NDVI or vegetation-related layers',
        useCase: 'Monitoring vegetation health, agricultural assessment, drought monitoring'
      },
      {
        type: 'vci_analysis',
        name: 'Vegetation Condition Index (VCI)',
        description: 'Calculates VCI to assess drought conditions and vegetation stress. Provides drought severity classification and temporal patterns.',
        expectedOutputs: ['VCI time-series', 'Drought condition assessment', 'Severity classification', 'Threshold analysis'],
        processingTime: 'Medium (1-3 minutes)',
        dataRequirements: 'NDVI or vegetation-related layers',
        useCase: 'Drought monitoring, agricultural risk assessment, early warning systems'
      },
      {
        type: 'anomaly_detection',
        name: 'Anomaly Detection',
        description: 'Identifies statistical anomalies in temporal data using advanced statistical methods. Detects unusual patterns and outliers.',
        expectedOutputs: ['Anomaly events', 'Statistical analysis', 'Deviation metrics', 'Severity classification'],
        processingTime: 'Medium (2-4 minutes)',
        dataRequirements: 'Any temporal layer with sufficient data points',
        useCase: 'Environmental monitoring, change detection, disaster assessment'
      },
      {
        type: 'seasonal_patterns',
        name: 'Seasonal Pattern Analysis',
        description: 'Analyzes seasonal and cyclical patterns in temporal data. Identifies recurring patterns and seasonal trends.',
        expectedOutputs: ['Monthly averages', 'Seasonal trends', 'Cyclical pattern detection', 'Pattern visualization'],
        processingTime: 'Medium (2-4 minutes)',
        dataRequirements: 'At least 12 months of temporal data',
        useCase: 'Climate analysis, agricultural planning, seasonal forecasting'
      }
    ];

    res.json({
      success: true,
      data: analysisTypes,
      message: 'Available analysis types retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error getting analysis types:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve analysis types',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @swagger
 * /api/time-series/visualization/{analysisType}:
 *   get:
 *     summary: Get visualization data for time-series analysis
 *     description: Returns chart data and visualization configuration for displaying time-series results
 *     tags:
 *       - Time Series Analysis
 *     parameters:
 *       - in: path
 *         name: analysisType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [ndvi_trend, vci_analysis, anomaly_detection, seasonal_patterns]
 *         description: Type of analysis to visualize
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for visualization
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for visualization
 *       - in: query
 *         name: layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers
 *     responses:
 *       200:
 *         description: Visualization data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     chartType:
 *                       type: string
 *                     chartData:
 *                       type: object
 *                     chartOptions:
 *                       type: object
 */
router.get('/visualization/:analysisType', async (req, res) => {
  try {
    const { analysisType } = req.params;
    const { startDate, endDate, layers } = req.query;

    // Validate parameters
    if (!startDate || !endDate || !layers) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters',
        details: ['startDate, endDate, and layers are required']
      });
    }

    // Generate visualization configuration based on analysis type
    let visualizationData;
    
    switch (analysisType) {
      case 'ndvi_trend':
        visualizationData = {
          chartType: 'line',
          chartData: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
              label: 'NDVI Trend',
              data: [0.3, 0.35, 0.45, 0.6, 0.7, 0.65, 0.6, 0.55, 0.5, 0.4, 0.35, 0.3],
              borderColor: 'rgb(34, 197, 94)',
              backgroundColor: 'rgba(34, 197, 94, 0.1)',
              tension: 0.4
            }]
          },
          chartOptions: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: false,
                min: -1,
                max: 1,
                title: { display: true, text: 'NDVI Value' }
              },
              x: {
                title: { display: true, text: 'Time Period' }
              }
            }
          }
        };
        break;
        
      case 'vci_analysis':
        visualizationData = {
          chartType: 'bar',
          chartData: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
              label: 'VCI (%)',
              data: [45, 50, 65, 80, 85, 75, 70, 65, 60, 55, 50, 45],
              backgroundColor: function(context: any) {
                const value = context.parsed.y;
                if (value >= 50) return 'rgba(34, 197, 94, 0.8)'; // Normal - Green
                if (value >= 35) return 'rgba(251, 191, 36, 0.8)'; // Mild - Yellow
                if (value >= 20) return 'rgba(249, 115, 22, 0.8)'; // Moderate - Orange
                if (value >= 10) return 'rgba(239, 68, 68, 0.8)'; // Severe - Red
                return 'rgba(127, 29, 29, 0.8)'; // Extreme - Dark Red
              }
            }]
          },
          chartOptions: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true,
                max: 100,
                title: { display: true, text: 'VCI (%)' }
              }
            }
          }
        };
        break;
        
      default:
        return res.status(400).json({
          success: false,
          error: 'Unsupported analysis type for visualization',
          details: [`Analysis type '${analysisType}' does not support visualization`]
        });
    }

    res.json({
      success: true,
      data: visualizationData,
      message: 'Visualization data retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error getting visualization data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve visualization data',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
