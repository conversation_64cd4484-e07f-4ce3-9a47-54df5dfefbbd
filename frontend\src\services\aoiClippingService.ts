/**
 * Unified AOI Clipping Service
 * 
 * This service standardizes AOI clipping across all components to ensure
 * consistent behavior between preview, rendering, and downloads.
 * 
 * Addresses the following issues:
 * - Multiple clipping implementations with different logic
 * - Hardcoded 'the_geom' vs actual geometry field names
 * - Inconsistent geometry processing
 * - Different error handling approaches
 */

import { convertGeoJSONToWKT } from '../utils/wktConverter';
import { getLayerSchema } from './boundaryService';

export interface AOIClippingOptions {
  geometry?: GeoJSON.Geometry;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  layerName: string;
  geometryField?: string;
  maxWKTLength?: number;
  enableFallback?: boolean;
}

export interface ClippingResult {
  method: 'CQL_FILTER' | 'BBOX' | 'NONE';
  parameters: Record<string, string>;
  success: boolean;
  error?: string;
  geometryField?: string;
  wktLength?: number;
}

/**
 * Geometry field cache to avoid repeated DescribeFeatureType requests
 */
const geometryFieldCache = new Map<string, string>();

/**
 * Unified AOI clipping function that works consistently across all components
 * Note: This is now synchronous to work in render functions. Geometry field detection
 * should be done beforehand and passed via geometryField option.
 */
export function applyAOIClipping(options: AOIClippingOptions): ClippingResult {
  const {
    geometry,
    bounds,
    layerName,
    geometryField: providedGeometryField,
    maxWKTLength = 6000,
    enableFallback = true
  } = options;

  console.log(`🔧 Applying unified AOI clipping for layer "${layerName}"`);

  // Method 1: CQL_FILTER with precise geometry (preferred)
  if (geometry) {
    try {
      // Get or use provided geometry field (detection should be done beforehand)
      const geometryField = getGeometryFieldSync(layerName, providedGeometryField);
      
      // Check if this is a raster layer (skip CQL for raster)
      const isRasterLayer = isRasterLayerType(layerName);
      if (isRasterLayer) {
        console.log(`🗺️ Raster layer detected: "${layerName}" - skipping CQL, using BBOX fallback`);
        throw new Error('Raster layer - CQL not supported');
      }

      // Convert geometry to WKT
      const wkt = convertGeoJSONToWKT(geometry, true, maxWKTLength);
      
      // Check WKT length
      if (wkt.length > maxWKTLength) {
        console.warn(`⚠️ WKT too long (${wkt.length} chars) for layer "${layerName}", falling back to BBOX`);
        throw new Error(`WKT too long: ${wkt.length} characters exceeds ${maxWKTLength} limit`);
      }

      // Generate CQL filter
      const cqlFilter = `INTERSECTS(${geometryField}, ${wkt})`;
      
      console.log(`✂️ CQL clipping applied for "${layerName}": ${geometryField} field, ${wkt.length} char WKT`);
      
      return {
        method: 'CQL_FILTER',
        parameters: {
          'CQL_FILTER': cqlFilter
        },
        success: true,
        geometryField,
        wktLength: wkt.length
      };

    } catch (error) {
      console.warn(`❌ CQL clipping failed for "${layerName}":`, error);
      
      // Fall through to BBOX method if enabled
      if (!enableFallback) {
        return {
          method: 'NONE',
          parameters: {},
          success: false,
          error: error instanceof Error ? error.message : 'CQL clipping failed'
        };
      }
    }
  }

  // Method 2: BBOX clipping (fallback or when only bounds available)
  if (bounds && enableFallback) {
    console.log(`📦 BBOX clipping applied for "${layerName}"`);
    
    const bbox = `${bounds.west},${bounds.south},${bounds.east},${bounds.north}`;
    
    return {
      method: 'BBOX',
      parameters: {
        'BBOX': bbox,
        'SRS': 'EPSG:4326'
      },
      success: true
    };
  }

  // Method 3: No clipping
  console.log(`⚠️ No clipping applied for "${layerName}" - no geometry or bounds available`);
  
  return {
    method: 'NONE',
    parameters: {},
    success: false,
    error: 'No geometry or bounds available for clipping'
  };
}

/**
 * Get geometry field name for a layer with caching (synchronous version)
 */
function getGeometryFieldSync(layerName: string, providedField?: string): string {
  // Use provided field if available
  if (providedField) {
    console.log(`Using provided geometry field for "${layerName}": ${providedField}`);
    return providedField;
  }

  // Check cache first
  if (geometryFieldCache.has(layerName)) {
    const cachedField = geometryFieldCache.get(layerName)!;
    console.log(`Using cached geometry field for "${layerName}": ${cachedField}`);
    return cachedField;
  }

  // Use common fallbacks (async detection should be done beforehand)
  const fallbackFields = ['geometry', 'the_geom', 'geom', 'wkb_geometry'];
  const fallbackField = fallbackFields[0]; // 'geometry' is more common than 'the_geom'

  console.warn(`⚠️ Using fallback geometry field for "${layerName}": ${fallbackField}`);
  console.warn(`Geometry field detection should be done beforehand for better accuracy`);

  return fallbackField;
}

/**
 * Async function to detect and cache geometry field for a layer
 * This should be called during layer discovery/initialization
 */
export async function detectAndCacheGeometryField(layerName: string): Promise<string> {
  // Check cache first
  if (geometryFieldCache.has(layerName)) {
    return geometryFieldCache.get(layerName)!;
  }

  // Detect geometry field
  try {
    console.log(`🔍 Detecting geometry field for "${layerName}"`);
    const schema = await getLayerSchema(layerName);

    if (schema?.geometryField) {
      console.log(`✅ Detected geometry field for "${layerName}": ${schema.geometryField}`);
      geometryFieldCache.set(layerName, schema.geometryField);
      return schema.geometryField;
    }
  } catch (error) {
    console.warn(`❌ Geometry field detection failed for "${layerName}":`, error);
  }

  // Use common fallbacks
  const fallbackFields = ['geometry', 'the_geom', 'geom', 'wkb_geometry'];
  const fallbackField = fallbackFields[0];

  console.warn(`⚠️ Using fallback geometry field for "${layerName}": ${fallbackField}`);
  geometryFieldCache.set(layerName, fallbackField);

  return fallbackField;
}

/**
 * Check if a layer is a raster layer type
 */
function isRasterLayerType(layerName: string): boolean {
  const rasterPatterns = [
    'mosaic', 'imagery', 'satellite', 'tiff', 'raster', 'rgb', 'infrared', 
    'coverage', 'landsat', 'sentinel', 'modis', 'dem', 'elevation'
  ];
  
  return rasterPatterns.some(pattern => 
    layerName.toLowerCase().includes(pattern)
  );
}

/**
 * Clear geometry field cache (useful for testing or when layer schemas change)
 */
export function clearGeometryFieldCache(): void {
  geometryFieldCache.clear();
  console.log('🧹 Geometry field cache cleared');
}

/**
 * Get current geometry field cache (for debugging)
 */
export function getGeometryFieldCache(): Record<string, string> {
  return Object.fromEntries(geometryFieldCache);
}

/**
 * Apply clipping parameters to a URLSearchParams object
 */
export function applyClippingToParams(params: URLSearchParams, clippingResult: ClippingResult): void {
  if (clippingResult.success) {
    Object.entries(clippingResult.parameters).forEach(([key, value]) => {
      params.set(key, value);
    });
  }
}

/**
 * Generate tight bounds from geometry for raster layer clipping
 */
export function calculateTightBounds(geometry: GeoJSON.Geometry): {
  west: number;
  south: number;
  east: number;
  north: number;
} | null {
  try {
    let minLng = Infinity, minLat = Infinity;
    let maxLng = -Infinity, maxLat = -Infinity;

    const processCoordinates = (coords: any) => {
      if (Array.isArray(coords[0])) {
        coords.forEach(processCoordinates);
      } else {
        const [lng, lat] = coords;
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      }
    };

    if (geometry.type === 'Polygon') {
      geometry.coordinates.forEach(processCoordinates);
    } else if (geometry.type === 'MultiPolygon') {
      geometry.coordinates.forEach(polygon => 
        polygon.forEach(processCoordinates)
      );
    } else {
      throw new Error(`Unsupported geometry type: ${geometry.type}`);
    }

    return {
      west: minLng,
      south: minLat,
      east: maxLng,
      north: maxLat
    };
  } catch (error) {
    console.error('Failed to calculate tight bounds:', error);
    return null;
  }
}
