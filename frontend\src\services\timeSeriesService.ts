import { API_CONFIG } from '../config';

export interface TimeSeriesQuery {
  geometry: GeoJSON.Polygon;
  startDate: string;
  endDate: string;
  analysisType: 'static_mosaic' | 'ndvi_trend' | 'vci_analysis' | 'anomaly_detection' | 'seasonal_patterns';
  layers: string[];
  aoiId?: number;
}

export interface TimeSeriesResult {
  analysisId: string;
  analysisType: string;
  dateRange: { startDate: string; endDate: string };
  geometry: GeoJSON.Polygon;
  products: {
    staticMosaic?: StaticMosaicProduct;
    ndviTrend?: NDVITrendProduct;
    vciAnalysis?: VCIAnalysisProduct;
    anomalies?: AnomalyProduct;
    seasonalPatterns?: SeasonalProduct;
  };
  metadata: {
    processingTime: number;
    dataPoints: number;
    qualityScore: number;
    generatedAt: string;
  };
}

export interface StaticMosaicProduct {
  type: 'static_mosaic';
  description: string;
  wmsUrl: string;
  downloadUrl?: string;
  coverage: {
    startDate: string;
    endDate: string;
    cloudCover: number;
  };
}

export interface NDVITrendProduct {
  type: 'ndvi_trend';
  description: string;
  trendData: {
    dates: string[];
    values: number[];
    trend: 'increasing' | 'decreasing' | 'stable';
    slope: number;
    correlation: number;
  };
  stackedLayers: string[];
  visualizationUrl: string;
  statistics: {
    mean: number;
    min: number;
    max: number;
    stdDev: number;
  };
}

export interface VCIAnalysisProduct {
  type: 'vci_analysis';
  description: string;
  vciData: {
    dates: string[];
    vciValues: number[];
    droughtConditions: Array<{
      date: string;
      severity: 'normal' | 'mild' | 'moderate' | 'severe' | 'extreme';
      vciValue: number;
    }>;
  };
  thresholds: {
    extreme: number;
    severe: number;
    moderate: number;
    mild: number;
  };
}

export interface AnomalyProduct {
  type: 'anomaly_detection';
  description: string;
  anomalies: Array<{
    date: string;
    value: number;
    expectedValue: number;
    deviation: number;
    severity: 'low' | 'medium' | 'high';
    type: 'positive' | 'negative';
  }>;
  statistics: {
    totalAnomalies: number;
    anomalyRate: number;
    detectionMethod: string;
  };
}

export interface SeasonalProduct {
  type: 'seasonal_patterns';
  description: string;
  patterns: {
    monthlyAverages: Array<{ month: number; value: number; }>;
    seasonalTrends: Array<{
      season: 'spring' | 'summer' | 'autumn' | 'winter';
      trend: 'increasing' | 'decreasing' | 'stable';
      averageValue: number;
    }>;
    cyclicalPatterns: {
      detected: boolean;
      period: number;
      amplitude: number;
    };
  };
}

export interface AnalysisType {
  type: string;
  name: string;
  description: string;
  expectedOutputs: string[];
  processingTime: string;
  dataRequirements: string;
  useCase: string;
}

export class TimeSeriesService {
  private static readonly API_URL = `${API_CONFIG.BASE_URL}/time-series`;

  /**
   * Perform time-series analysis
   */
  static async performAnalysis(query: TimeSeriesQuery): Promise<TimeSeriesResult> {
    try {
      console.log('🕒 Starting time-series analysis:', query.analysisType);

      const response = await fetch(`${this.API_URL}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(query),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Analysis failed');
      }

      console.log('✅ Time-series analysis completed:', result.data.analysisId);
      return result.data;

    } catch (error) {
      console.error('❌ Time-series analysis failed:', error);
      throw error;
    }
  }

  /**
   * Get available analysis types
   */
  static async getAnalysisTypes(): Promise<AnalysisType[]> {
    try {
      const response = await fetch(`${this.API_URL}/types`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get analysis types');
      }

      return result.data;

    } catch (error) {
      console.error('❌ Failed to get analysis types:', error);
      throw error;
    }
  }

  /**
   * Get visualization data for analysis results
   */
  static async getVisualizationData(
    analysisType: string,
    startDate: string,
    endDate: string,
    layers: string[]
  ): Promise<any> {
    try {
      const params = new URLSearchParams({
        startDate,
        endDate,
        layers: layers.join(',')
      });

      const response = await fetch(`${this.API_URL}/visualization/${analysisType}?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get visualization data');
      }

      return result.data;

    } catch (error) {
      console.error('❌ Failed to get visualization data:', error);
      throw error;
    }
  }

  /**
   * Format analysis result for display
   */
  static formatAnalysisResult(result: TimeSeriesResult): {
    title: string;
    summary: string;
    keyFindings: string[];
    recommendations: string[];
  } {
    const { analysisType, products, metadata } = result;

    switch (analysisType) {
      case 'static_mosaic':
        return {
          title: 'Static Mosaic Generated',
          summary: `Composite image created from ${metadata.dataPoints} data points with ${metadata.qualityScore}% quality score.`,
          keyFindings: [
            `Coverage period: ${result.dateRange.startDate} to ${result.dateRange.endDate}`,
            `Processing time: ${metadata.processingTime}ms`,
            `Cloud coverage: ${products.staticMosaic?.coverage.cloudCover.toFixed(1)}%`
          ],
          recommendations: [
            'Use this mosaic for general visualization and overview analysis',
            'Consider time-series analysis for trend detection',
            'Download high-resolution version for detailed analysis'
          ]
        };

      case 'ndvi_trend':
        const ndviTrend = products.ndviTrend!;
        return {
          title: 'NDVI Trend Analysis Complete',
          summary: `Vegetation trend is ${ndviTrend.trendData.trend} with ${ndviTrend.trendData.correlation.toFixed(3)} correlation coefficient.`,
          keyFindings: [
            `Trend direction: ${ndviTrend.trendData.trend}`,
            `Average NDVI: ${ndviTrend.statistics.mean.toFixed(3)}`,
            `NDVI range: ${ndviTrend.statistics.min.toFixed(3)} to ${ndviTrend.statistics.max.toFixed(3)}`,
            `Standard deviation: ${ndviTrend.statistics.stdDev.toFixed(3)}`
          ],
          recommendations: [
            ndviTrend.trendData.trend === 'decreasing' ? 'Monitor for potential vegetation stress' : 'Vegetation health appears stable',
            'Compare with precipitation and temperature data',
            'Consider seasonal analysis for deeper insights'
          ]
        };

      case 'vci_analysis':
        const vciAnalysis = products.vciAnalysis!;
        const severeConditions = vciAnalysis.vciData.droughtConditions.filter(c => 
          c.severity === 'severe' || c.severity === 'extreme'
        );
        return {
          title: 'VCI Drought Analysis Complete',
          summary: `${severeConditions.length} periods of severe/extreme drought conditions detected.`,
          keyFindings: [
            `Severe drought periods: ${severeConditions.length}`,
            `Average VCI: ${(vciAnalysis.vciData.vciValues.reduce((a, b) => a + b, 0) / vciAnalysis.vciData.vciValues.length).toFixed(1)}%`,
            `Lowest VCI: ${Math.min(...vciAnalysis.vciData.vciValues).toFixed(1)}%`
          ],
          recommendations: [
            severeConditions.length > 0 ? 'Implement drought mitigation measures' : 'Continue monitoring for early warning',
            'Correlate with agricultural yield data',
            'Set up automated alerts for VCI thresholds'
          ]
        };

      case 'anomaly_detection':
        const anomalies = products.anomalies!;
        return {
          title: 'Anomaly Detection Complete',
          summary: `${anomalies.statistics.totalAnomalies} anomalies detected (${anomalies.statistics.anomalyRate.toFixed(1)}% anomaly rate).`,
          keyFindings: [
            `Total anomalies: ${anomalies.statistics.totalAnomalies}`,
            `Anomaly rate: ${anomalies.statistics.anomalyRate.toFixed(1)}%`,
            `Detection method: ${anomalies.statistics.detectionMethod}`
          ],
          recommendations: [
            'Investigate high-severity anomalies for potential causes',
            'Cross-reference with weather events and human activities',
            'Use for early warning system development'
          ]
        };

      case 'seasonal_patterns':
        const patterns = products.seasonalPatterns!;
        return {
          title: 'Seasonal Pattern Analysis Complete',
          summary: `${patterns.patterns.cyclicalPatterns.detected ? 'Cyclical patterns detected' : 'No clear cyclical patterns'} with ${patterns.patterns.monthlyAverages.length} months analyzed.`,
          keyFindings: [
            `Cyclical patterns: ${patterns.patterns.cyclicalPatterns.detected ? 'Yes' : 'No'}`,
            `Pattern period: ${patterns.patterns.cyclicalPatterns.period} months`,
            `Seasonal amplitude: ${patterns.patterns.cyclicalPatterns.amplitude.toFixed(3)}`
          ],
          recommendations: [
            'Use seasonal patterns for agricultural planning',
            'Incorporate into predictive models',
            'Monitor deviations from normal seasonal patterns'
          ]
        };

      default:
        return {
          title: 'Analysis Complete',
          summary: 'Time-series analysis has been completed successfully.',
          keyFindings: ['Analysis completed'],
          recommendations: ['Review results and consider next steps']
        };
    }
  }

  /**
   * Validate analysis query before submission
   */
  static validateQuery(query: Partial<TimeSeriesQuery>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!query.geometry) {
      errors.push('Geometry is required');
    }

    if (!query.startDate || !query.endDate) {
      errors.push('Start and end dates are required');
    }

    if (query.startDate && query.endDate && new Date(query.startDate) >= new Date(query.endDate)) {
      errors.push('Start date must be before end date');
    }

    if (!query.analysisType) {
      errors.push('Analysis type is required');
    }

    if (!query.layers || query.layers.length === 0) {
      errors.push('At least one layer is required');
    }

    // Check date range for specific analysis types
    if (query.startDate && query.endDate) {
      const daysDiff = Math.ceil((new Date(query.endDate).getTime() - new Date(query.startDate).getTime()) / (1000 * 60 * 60 * 24));
      
      if (query.analysisType === 'seasonal_patterns' && daysDiff < 365) {
        errors.push('Seasonal pattern analysis requires at least 12 months of data');
      }

      if ((query.analysisType === 'ndvi_trend' || query.analysisType === 'vci_analysis') && daysDiff < 90) {
        errors.push('Trend analysis requires at least 3 months of data for meaningful results');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
