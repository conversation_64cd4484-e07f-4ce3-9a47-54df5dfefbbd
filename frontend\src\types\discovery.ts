export interface TemporalDimension {
  name: string;
  units?: string;
  unitSymbol?: string;
  default?: string;
  multipleValues?: boolean;
  nearestValue?: boolean;
  current?: boolean;
  extent?: string;
  values?: string[];
}

export interface ServiceContact {
  organization?: string;
  person?: string;
  position?: string;
  email?: string;
  phone?: string;
  address?: {
    type?: string;
    address?: string;
    city?: string;
    stateOrProvince?: string;
    postCode?: string;
    country?: string;
  };
}

export interface ServiceMetadata {
  title?: string;
  abstract?: string;
  version?: string;
  contact?: ServiceContact;
  fees?: string;
  accessConstraints?: string;
  onlineResource?: string;
  maxWidth?: number;
  maxHeight?: number;
}

export interface LayerDiscovery {
  name: string;
  title: string;
  abstract: string;
  attribution?: string;
  type?: string;
  keywords: string[];
  bbox: {
    SRS?: string;
    minx?: string;
    miny?: string;
    maxx?: string;
    maxy?: string;
  };
  style: string;
  legendUrl: string;
  formats: string[];
  supports: {
    WMS: boolean;
    WFS: boolean;
    WMTS: boolean;
  };
  // GeoNode discovery fields
  category?: string;
  subtype?: string;
  workspace?: string;
  thumbnail_url?: string;
  detail_url?: string;
  download_urls?: any;
  // WMS rendering fields
  url?: string;
  layers?: string;
  format?: string;
  transparent?: boolean;
  version?: string;
  // Remote layer fields
  isRemote?: boolean;
  serviceType?: string;
  remoteUrl?: string;
  metadata?: {
    title?: string;
    abstract?: string;
    keywords?: string[];
    thumbnail?: string;
    detail_url?: string;
  };
  // Enhanced OGC metadata
  crs?: string[];
  temporal?: TemporalDimension;
  metadataUrls?: Array<{
    type: string;
    format: string;
    onlineResource: string;
  }>;
  dataUrls?: Array<{
    format: string;
    onlineResource: string;
  }>;
  featureListUrls?: Array<{
    format: string;
    onlineResource: string;
  }>;
  styles?: Array<{
    name: string;
    title?: string;
    abstract?: string;
    legendUrl?: string;
    styleSheetUrl?: string;
    styleUrl?: string;
  }>;
  dimensions?: TemporalDimension[];
  authorityUrls?: Array<{
    name: string;
    onlineResource: string;
  }>;
  identifiers?: Array<{
    authority: string;
    identifier: string;
  }>;
  minScaleDenominator?: number;
  maxScaleDenominator?: number;
  queryable?: boolean;
  cascaded?: number;
  opaque?: boolean;
  noSubsets?: boolean;
  fixedWidth?: number;
  fixedHeight?: number;
}
