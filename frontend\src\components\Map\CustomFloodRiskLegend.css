.custom-flood-risk-legend {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  width: 140px;
  flex-shrink: 0;
}

.custom-legend-header {
  margin-bottom: 8px;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 4px;
}

.custom-legend-title {
  font-size: 12px;
  font-weight: 700;
  color: #212529;
  margin: 0;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.custom-legend-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.custom-legend-row {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 0;
}

.custom-legend-color-box {
  width: 14px;
  height: 14px;
  border: 1px solid #333;
  border-radius: 2px;
  flex-shrink: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.custom-legend-label {
  font-size: 11px;
  font-weight: 500;
  color: #495057;
  flex: 1;
}

/* Hover effects */
.custom-legend-row:hover {
  background-color: #f8f9fa;
  border-radius: 3px;
  padding: 3px 2px;
  transition: all 0.2s ease;
}

.custom-legend-row:hover .custom-legend-color-box {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

.custom-legend-row:hover .custom-legend-label {
  color: #212529;
  font-weight: 600;
  transition: all 0.2s ease;
}

/* Animation for legend appearance */
.custom-flood-risk-legend {
  animation: customLegendFadeIn 0.3s ease-out;
}

@keyframes customLegendFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .custom-flood-risk-legend {
    padding: 8px;
    width: 120px;
  }

  .custom-legend-title {
    font-size: 10px;
  }

  .custom-legend-color-box {
    width: 12px;
    height: 12px;
  }

  .custom-legend-label {
    font-size: 10px;
  }
}
