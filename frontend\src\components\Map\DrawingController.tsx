import { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet-draw';

interface DrawingControllerProps {
  isDrawingMode: boolean;
  onCreated: (layer: any) => void;
}

const DrawingController: React.FC<DrawingControllerProps> = ({ isDrawingMode, onCreated }) => {
  const map = useMap();

  useEffect(() => {
    if (!map) return;

    let drawControl: L.Control.Draw | null = null;
    let drawnItems: L.FeatureGroup | null = null;

    if (isDrawingMode) {
      // Create feature group for drawn items
      drawnItems = new L.FeatureGroup();
      map.addLayer(drawnItems);

      // Create draw control
      drawControl = new L.Control.Draw({
        edit: {
          featureGroup: drawnItems,
        },
        draw: {
          polygon: true,
          rectangle: true,
          circle: false,
          marker: false,
          polyline: false,
          circlemarker: false
        }
      });

      map.addControl(drawControl);

      // Handle draw events
      const handleDrawCreated = (e: any) => {
        const layer = e.layer;
        drawnItems?.addLayer(layer);
        onCreated(layer);
      };

      map.on(L.Draw.Event.CREATED, handleDrawCreated);

      return () => {
        map.off(L.Draw.Event.CREATED, handleDrawCreated);
        if (drawControl) {
          map.removeControl(drawControl);
        }
        if (drawnItems) {
          map.removeLayer(drawnItems);
        }
      };
    }
  }, [map, isDrawingMode, onCreated]);

  return null; // This component doesn't render anything
};

export default DrawingController;
