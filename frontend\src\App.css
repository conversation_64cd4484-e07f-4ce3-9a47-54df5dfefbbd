/* Global header styles import */
@import './styles/headers.css';

/* Main app container */
.app-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-container {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

/* Main content container */
.main-content {
  display: flex !important;
  height: 100% !important;
  width: 100% !important;
  overflow: hidden;
}

/* Sidebar styles */
.sidebar-container {
  height: 100%;
  padding: 0;
  background-color: transparent; /* Remove background to avoid conflicts */
  overflow: visible; /* Allow content to determine overflow */
  transition: all 0.3s ease;
  flex-shrink: 0;
  /* Remove width settings - let the inner .sidebar handle width */
}



/* Map container styles */
.map-container {
  height: 100%;
  padding: 0;
  flex: 1;
  min-width: 0;
  transition: all 0.3s ease;
}

/* Ensure the map takes full height */
.leaflet-container {
  height: 100%;
  width: 100%;
}



/* Button styles */
.action-button {
  width: 100%;
  background-color: #0a4273;
  border: none;
  margin-bottom: 10px;
  padding: 8px 0;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #063057;
}

/* Input styles */
.date-input {
  margin-bottom: 10px;
}

/* Section styles */
.section-title {
  font-weight: 600;
  margin-bottom: 15px;
  margin-top: 20px;
}

.section-content {
  margin-bottom: 20px;
}

/* Checkbox styles */
.layer-checkbox {
  margin-bottom: 8px;
}

.layer-label {
  margin-left: 8px;
}

/* Search input */
.search-input {
  width: 100%;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar-container, .map-container {
    height: auto;
  }

  .sidebar-container {
    max-height: 50vh;
    overflow-y: scroll;
  }

  .map-container {
    height: 50vh;
  }

  .leaflet-container {
    height: 50vh;
  }
}

@media (max-width: 480px) {
  .sidebar-container {
    max-height: 40vh; /* Less height on very small screens */
  }

  .map-container {
    height: 60vh;
  }

  .leaflet-container {
    height: 60vh;
  }
}