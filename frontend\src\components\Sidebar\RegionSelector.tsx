import React, { useState } from 'react';
import { But<PERSON>, Form, Badge } from 'react-bootstrap';
import { Pentagon, Search, Trash2 } from 'lucide-react';
// import InteractiveBoundaryFilter from '../Boundary/InteractiveBoundaryFilter'; // Commented out as not used

interface RegionSelectorProps {
  onSearch: (query: string) => void;
  onDrawModeToggle: (isDrawing: boolean) => void;
  isDrawingMode: boolean;
  hasDrawnArea: boolean;
  onClearDrawnArea: () => void;
  // Regional AOI props
  aoiMethod: 'drawn' | 'regional';
  onAOIMethodChange: (method: 'drawn' | 'regional') => void;
  hasRegionalSelection: boolean;
  onConfigureRegions: () => void;
  onClearRegionalSelection: () => void;
  // Predefined polygon props
  onPredefinedPolygon: (size: string) => void;
  // Interactive boundary filtering props
  onBoundaryHighlight?: (features: GeoJSON.Feature[]) => void;
  onBoundaryRegionSelection?: (features: GeoJSON.Feature[]) => void;
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  onSearch,
  onDrawModeToggle,
  isDrawingMode,
  hasDrawnArea,
  onClearDrawnArea,
  aoiMethod,
  onAOIMethodChange,
  hasRegionalSelection,
  onConfigureRegions,
  onClearRegionalSelection,
  onPredefinedPolygon,
  onBoundaryHighlight,
  onBoundaryRegionSelection
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  const handleDrawToggle = () => {
    onDrawModeToggle(!isDrawingMode);
  };

  return (
    <div className="region-selector section">

      {/* AOI Method Selection - Administrative boundaries option removed */}
      <div className="mb-3">
        <div className="mb-2">
          <small className="text-muted">Choose how to define your area:</small>
        </div>

        <Form.Check
          type="radio"
          id="aoi-method-drawn"
          name="aoi-method"
          label="Draw on Map"
          checked={aoiMethod === 'drawn'}
          onChange={() => onAOIMethodChange('drawn')}
          className="mb-2"
          style={{ fontSize: '0.9rem' }}
        />

        {/* Administrative boundaries radio button removed as requested */}
        {/* <Form.Check
          type="radio"
          id="aoi-method-regional"
          name="aoi-method"
          label="Select by Administrative Boundaries"
          checked={aoiMethod === 'regional'}
          onChange={() => onAOIMethodChange('regional')}
          className="mb-3"
          style={{ fontSize: '0.9rem' }}
        /> */}
      </div>

      {/* Drawing Controls - Only show when drawn method is selected */}
      {aoiMethod === 'drawn' && (
        <div className="mb-3">
        {/* Drawing Method Selection */}
        <div className="mb-3">
          <small className="text-muted d-block mb-2">Choose drawing method:</small>
          <div className="d-flex gap-2 align-items-center mb-2">
            <Button
              className={`action-button flex-grow-1 d-flex align-items-center justify-content-center ${isDrawingMode ? 'btn-warning' : 'btn-primary'}`}
              onClick={handleDrawToggle}
              style={{ fontSize: '0.85rem', padding: '0.5rem' }}
            >
              <Pentagon size={16} className="me-2" />
              {isDrawingMode ? 'Cancel Drawing' : 'Draw Polygon'}
            </Button>

            {hasDrawnArea && (
              <Button
                variant="outline-danger"
                size="sm"
                onClick={onClearDrawnArea}
                title="Clear drawn area"
              >
                <Trash2 size={16} />
              </Button>
            )}
          </div>

          {/* Predefined Polygon Dropdown */}
          <div className="mb-2">
            <Form.Select
              size="sm"
              onChange={(e) => {
                if (e.target.value) {
                  onPredefinedPolygon(e.target.value);
                  e.target.value = ''; // Reset selection
                }
              }}
              style={{
                fontSize: '0.85rem',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                color: '#333'
              }}
            >
              <option value="">Or select predefined area...</option>
              <option value="25x25">25-by-25 km</option>
              <option value="50x50">50-by-50 km</option>
              <option value="100x100">100-by-100 km</option>
              <option value="250x250">250-by-250 km</option>
            </Form.Select>
          </div>
        </div>

        {/* Drawing mode buttons removed as requested */}
        {/* {isDrawingMode && (
          <>
            <div className="section mb-3">
              <Button
                variant="outline-primary"
                className="w-100 d-flex align-items-center justify-content-center"
                style={{
                  fontSize: '0.85rem',
                  padding: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <Filter size={16} className="me-2" />
                Draw Polygon

              </Button>
            </div>
            <div className="section mb-3">
              <Button
                variant="outline-primary"
                className="w-100 d-flex align-items-center justify-content-center"
                style={{
                  fontSize: '0.85rem',
                  padding: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <Filter size={16} className="me-2" />
                SA Country Boundary
              </Button>
            </div>
            <div className="section mb-3">
              <Button
                variant="outline-primary"
                className="w-100 d-flex align-items-center justify-content-center"
                style={{
                  fontSize: '0.85rem',
                  padding: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <Filter size={16} className="me-2" />
                SA Municipal Boundary
              </Button>
            </div>
            <div className="section mb-3">
              <Button
                variant="outline-primary"
                className="w-100 d-flex align-items-center justify-content-center"
                style={{
                  fontSize: '0.85rem',
                  padding: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <Filter size={16} className="me-2" />
                SA Place Names
              </Button>
            </div>
            <div className="section mb-3">
              <Button
                variant="outline-primary"
                className="w-100 d-flex align-items-center justify-content-center"
                style={{
                  fontSize: '0.85rem',
                  padding: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <Filter size={16} className="me-2" />
                SA Provincial Boundary
              </Button>
            </div>
            <div className="section mb-3">
              <Button
                variant="outline-primary"
                className="w-100 d-flex align-items-center justify-content-center"
                style={{
                  fontSize: '0.85rem',
                  padding: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <Filter size={16} className="me-2" />
                Build Filter
              </Button>
            </div>
            <div className="mt-2">
              <Badge bg="info" className="w-100 text-center py-2">
                Click on the map to start drawing your area of interest
              </Badge>
            </div>
          </>
        )} */}

        {hasDrawnArea && !isDrawingMode && (
          <div className="mt-2">
            <Badge bg="success" className="w-100 text-center py-2">
              Area of interest defined - ready for analysis
            </Badge>
          </div>
        )}
      </div>
      )}

      {/* Regional Controls - Removed as requested */}
      {/* {aoiMethod === 'regional' && (
        <div className="mb-3">
          <InteractiveBoundaryFilter
            onRegionSelection={(features) => {
              console.log('🎯 Interactive boundary region selected:', features.length, 'features');
              if (onBoundaryRegionSelection) {
                onBoundaryRegionSelection(features);
              }
              // Mark as having regional selection
              if (features.length > 0) {
                onConfigureRegions(); // This triggers hasRegionalSelection state
              }
            }}
            onHighlightFeatures={(features) => {
              console.log('🗺️ Highlighting', features.length, 'boundary features on map');
              if (onBoundaryHighlight) {
                onBoundaryHighlight(features);
              }
            }}
            isVisible={true}
          />

          {hasRegionalSelection && (
            <div className="d-flex gap-2 mt-2">
              <Badge bg="success" className="flex-grow-1 text-center py-2">
                Interactive boundary selection active
              </Badge>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={onClearRegionalSelection}
                title="Clear boundary selection"
              >
                <Trash2 size={16} />
              </Button>
            </div>
          )}
        </div>
      )} */}

      {/* Search Controls */}
      {/* <Form onSubmit={handleSubmit}>
        <Form.Control
          type="text"
          placeholder="Enter location or coordinates"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="region-input mb-2"
        />
        <Button
          type="submit"
          className="search-button w-100 d-flex align-items-center justify-content-center"
          style={{ fontSize: '0.85rem', padding: '0.5rem' }}
        >
          <Search size={16} className="me-2" />
          Search Location
        </Button>
      </Form> */}
    </div>
  );
};

export default RegionSelector;