import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './swagger';

// Import routes
import { owsRouter } from './routes/ows';
import { datasetRouter } from './routes/datasets';
import { capabilitiesRouter } from './routes/capabilities';
import alertRulesRouter from './routes/alertRules';
import alertEventsRouter from './routes/alertEvents';
import timeSeriesRouter from './routes/timeSeries';

// Import services
import { validateGeoServerConnection } from './utils/geoServerValidator';
import { AlertEngine } from './services/alertEngine';
import { AlertSocket } from './websocket/alertSocket';
import { NotificationService } from './services/notificationService';
import { DatabaseService } from './services/databaseService';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Cache-Control', 'X-Requested-With'],
  credentials: false, // Explicitly set for download requests
  maxAge: 86400, // Cache preflight for 24 hours
  optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '50mb' })); // Increased limit for large geometry data
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // Increased limit for large geometry data
app.use(express.static('public')); // Serve static files from public directory

// Add multer for handling multipart/form-data (for large geometry in downloads)
const multer = require('multer');
const upload = multer({
  limits: {
    fieldSize: 50 * 1024 * 1024, // 50MB field size limit for large geometry data
    fields: 20, // Maximum number of fields
    parts: 30   // Maximum number of parts
  }
});
app.use('/api/ows/multi-layer-download', upload.none()); // Handle FormData without files

// Conditionally import PostGIS-related modules
let roiRouter: any = null;
let testConnection: any = null;
let checkPostGIS: any = null;
let closeConnection: any = null;

// Check if PostGIS should be enabled
const ENABLE_POSTGIS = process.env.ENABLE_POSTGIS !== 'false' && process.env.NODE_ENV !== 'minimal';

// Global variables for alerting system
let alertSocket: AlertSocket | null = null;
let notificationService: NotificationService | null = null;
let alertEngine: AlertEngine | null = null;

if (ENABLE_POSTGIS) {
  try {
    const roiModule = require('./routes/roi');
    const dbModule = require('./config/database');
    
    roiRouter = roiModule.default;
    testConnection = dbModule.testConnection;
    checkPostGIS = dbModule.checkPostGIS;
    closeConnection = dbModule.closeConnection;
    
    console.log('PostGIS modules loaded successfully');
  } catch (error) {
    console.warn('PostGIS modules not available, running without spatial features:', (error as Error).message);
    console.log('To enable PostGIS features, ensure database dependencies are installed');
  }
} 

/**
 * @swagger
 * /health:
 *   get:
 *     summary: System health check
 *     description: |
 *       Comprehensive health check endpoint that verifies the status of all system components:
 *       - Database connectivity
 *       - PostGIS spatial extensions
 *       - GeoServer connectivity
 *       - Dataset service availability
 *       - Feature availability matrix
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: System health status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthStatus'
 *       500:
 *         description: System error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Health check endpoint
app.get('/health', async (req, res) => {
  let dbStatus = false;
  let postgisStatus = false;
  let datasetStatus = 'checking';
  
  if (testConnection && checkPostGIS) {
    try {
      dbStatus = await testConnection();
      postgisStatus = await checkPostGIS();
    } catch (error) {
      console.warn('Database health check failed:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Check dataset service
  try {
    const { datasetService } = await import('./services/datasetService');
    await datasetService.getAllDatasets();
    datasetStatus = 'healthy';
  } catch (error) {
    datasetStatus = 'unavailable';
    console.warn('Dataset service health check failed:', (error as Error).message);
  }

  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'UIEngine',
    geoserver: process.env.GEOSERVER_URL,
    database: dbStatus ? 'connected' : 'disconnected',
    postgis: postgisStatus ? 'available' : 'unavailable',
    datasets: datasetStatus,
    features: {
      core: true,
      spatial: !!roiRouter,
      alerts: true,
      analytics: true,
      reports: dbStatus,
      advanced_spatial: postgisStatus
    }
  });
});

// API routes
app.use('/api/ows', owsRouter);
app.use('/api/ows', capabilitiesRouter);
app.use('/api/datasets', datasetRouter);
app.use('/api/alert-rules', alertRulesRouter);
app.use('/api/alert-events', alertEventsRouter);
app.use('/api/reports', require('./routes/reports').default);
app.use('/api/time-series', timeSeriesRouter);

// OWS routes (for GeoServer proxy)
app.use('/ows', owsRouter);

// Conditionally add PostGIS routes if available
if (roiRouter) {
  app.use('/api/roi', roiRouter);
} else {
  app.use('/api/roi', (req, res) => {
    res.status(503).json({
      success: false,
      error: 'PostGIS features not available',
      message: 'PostGIS database is not configured. Please check your database connection and ensure PostGIS dependencies are installed.',
      fallback: true
    });
  });
  console.log('PostGIS not available - ROI endpoints will return 503');
}

// Add Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const server = createServer(app);

// Graceful shutdown
process.on('SIGTERM', async () => {
  if (closeConnection) {
    try {
      await closeConnection();
    } catch (error) {
      console.warn('Warning during database shutdown:', error instanceof Error ? error.message : 'Unknown error');
    }
  }
  process.exit(0);
});

process.on('SIGINT', async () => {  
  // Shutdown alerting system components
  if (alertEngine) {
    (alertEngine as AlertEngine).stop();
    console.log('Alert engine stopped');
  }
  
  if (alertSocket) {
    (alertSocket as AlertSocket).close();
    console.log('WebSocket server closed');
  }
  
  // Close database connections
  if (closeConnection) {
    try {
      await closeConnection();
    } catch (error) {
      console.warn('Warning during database shutdown:', error instanceof Error ? error.message : 'Unknown error');
    }
  }
  
  try {
    await DatabaseService.closePool();
    console.log('Database pool closed');
  } catch (error) {
    console.warn('Warning during database pool shutdown:', error instanceof Error ? error.message : 'Unknown error');
  }
  
  process.exit(0);
});

server.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`);

  // Check if database is disabled via environment variable
  const databaseDisabled = process.env.DISABLE_DATABASE === 'true' || process.env.DISABLE_POSTGIS === 'true';

  if (databaseDisabled) {
    console.log('🚫 Database connections disabled via environment variables');
    console.log('ℹ️ Running in database-free mode');
  } else {
    // Test database connection on startup (non-blocking)
    if (testConnection && checkPostGIS) {
      try {
        await testConnection();
        await checkPostGIS();
        console.log('PostGIS integration verified and ready');
      } catch (error) {
        console.warn('Database connection failed, continuing without PostGIS features');
      }
    } else {
      console.log('ℹ️ Running in minimal mode without PostGIS dependencies');
    }
  }

  // Validate GeoServer connection
  try {
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://10.150.16.184/geoserver';
    await validateGeoServerConnection(geoserverUrl);
  } catch (error: any) {
    console.warn('GeoServer connection validation failed:', error?.message || 'Unknown error');
    console.warn('Layer discovery and capabilities may be limited');
  }
  // Phase 3: Initialize Real-time Alerting System
  try {
    // Initialize WebSocket server for real-time alerts
    alertSocket = new AlertSocket(server);
    console.log('WebSocket server initialized for real-time alerts');    // Initialize notification service
    notificationService = new NotificationService();
    notificationService.setSocketIO(alertSocket.getIO());
    await notificationService.testNotifications();
    console.log('Notification service initialized');

    // Initialize alert engine (will use fallback mode if database unavailable)
    alertEngine = new AlertEngine();
    
    // Test database connection for alert engine
    const dbConnected = await DatabaseService.testConnection();
    if (dbConnected) {
      // Start alert engine with database features
      await alertEngine.start(1);
      console.log('Alert engine started with database features (1-minute polling interval)');
    } else {
      // Start alert engine in fallback mode
      await alertEngine.startFallbackMode(1);
      console.log('Alert engine started in fallback mode (in-memory only)');
    }

  } catch (error) {
    console.error('Failed to initialize alerting system:', error);
  }
});
