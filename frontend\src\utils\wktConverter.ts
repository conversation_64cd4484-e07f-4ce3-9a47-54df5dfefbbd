/**
 * Utility functions for converting between GeoJSON and WKT (Well-Known Text) formats
 * Used for AOI clipping with CQL filters in WMS requests
 */

export interface GeoJSONGeometry {
  type: string;
  coordinates: any;
}

/**
 * Simplify coordinates by reducing precision and removing redundant points
 * @param coordinates Array of coordinate pairs
 * @param tolerance Simplification tolerance (default: 0.001 degrees ≈ 100m)
 * @param aggressive Whether to use aggressive simplification for very complex geometries
 * @returns Simplified coordinates array
 */
function simplifyCoordinates(coordinates: number[][], tolerance: number = 0.001, aggressive: boolean = false): number[][] {
  if (coordinates.length <= 3) return coordinates; // Keep minimal polygons

  // Use more aggressive simplification for very complex geometries
  const effectiveTolerance = aggressive ? tolerance * 5 : tolerance;
  const minPoints = aggressive ? 5 : 10;
  const maxPoints = aggressive ? 50 : 200; // Limit maximum points for very complex shapes

  const simplified: number[][] = [coordinates[0]]; // Always keep first point

  // If we have too many points, use a step-based approach for aggressive simplification
  if (aggressive && coordinates.length > 500) {
    const step = Math.ceil(coordinates.length / maxPoints);
    for (let i = step; i < coordinates.length - 1; i += step) {
      simplified.push([
        Math.round(coordinates[i][0] * 1000) / 1000, // Round to 3 decimal places for aggressive
        Math.round(coordinates[i][1] * 1000) / 1000
      ]);
    }
  } else {
    // Normal Douglas-Peucker-style simplification
    for (let i = 1; i < coordinates.length - 1; i++) {
      const prev = simplified[simplified.length - 1];
      const curr = coordinates[i];
      const next = coordinates[i + 1];

      // Calculate distance from current point to line between prev and next
      const distance = pointToLineDistance(curr, prev, next);

      // Keep point if it's significant or if we're getting too sparse
      if (distance > effectiveTolerance || simplified.length < minPoints) {
        const precision = aggressive ? 1000 : 10000; // Less precision for aggressive
        simplified.push([
          Math.round(curr[0] * precision) / precision,
          Math.round(curr[1] * precision) / precision
        ]);
      }

      // Stop if we've reached max points for aggressive mode
      if (aggressive && simplified.length >= maxPoints) {
        break;
      }
    }
  }

  // Always keep last point (should be same as first for closed polygons)
  simplified.push(coordinates[coordinates.length - 1]);

  console.log(`🔧 Simplified coordinates (${aggressive ? 'aggressive' : 'normal'}): ${coordinates.length} → ${simplified.length} points`);
  return simplified;
}

/**
 * Calculate perpendicular distance from point to line
 */
function pointToLineDistance(point: number[], lineStart: number[], lineEnd: number[]): number {
  const [px, py] = point;
  const [x1, y1] = lineStart;
  const [x2, y2] = lineEnd;

  const A = px - x1;
  const B = py - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;

  if (lenSq === 0) return Math.sqrt(A * A + B * B);

  const param = dot / lenSq;

  let xx, yy;
  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  const dx = px - xx;
  const dy = py - yy;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Converts a GeoJSON geometry to WKT format with automatic simplification for URL length limits
 * @param geometry GeoJSON geometry object (supports both our interface and standard GeoJSON.Geometry)
 * @param simplify Whether to simplify geometry to reduce URL length (default: true)
 * @param maxLength Maximum WKT length before aggressive simplification (default: 6000)
 * @returns WKT string representation
 */
export function convertGeoJSONToWKT(geometry: GeoJSONGeometry | GeoJSON.Geometry, simplify: boolean = true, maxLength: number = 6000): string {
  // Try normal simplification first
  const normalWKT = convertGeoJSONToWKTInternal(geometry, simplify, false);

  if (!simplify || normalWKT.length <= maxLength) {
    return normalWKT;
  }

  console.log(`⚠️ WKT too long (${normalWKT.length} chars), trying aggressive simplification...`);

  // Try aggressive simplification
  const aggressiveWKT = convertGeoJSONToWKTInternal(geometry, true, true);

  if (aggressiveWKT.length <= maxLength) {
    console.log(`✅ Aggressive simplification successful: ${normalWKT.length} → ${aggressiveWKT.length} chars`);
    return aggressiveWKT;
  }

  console.warn(`❌ Even aggressive simplification too long (${aggressiveWKT.length} chars), returning anyway`);
  return aggressiveWKT;
}

/**
 * Internal WKT conversion function
 * @param geometry GeoJSON geometry object
 * @param simplify Whether to simplify geometry
 * @param aggressive Whether to use aggressive simplification
 * @returns WKT string representation
 */
function convertGeoJSONToWKTInternal(geometry: GeoJSONGeometry | GeoJSON.Geometry, simplify: boolean = true, aggressive: boolean = false): string {
  if (!geometry || !geometry.type) {
    throw new Error('Invalid GeoJSON geometry provided');
  }

  // Handle GeometryCollection
  if (geometry.type === 'GeometryCollection') {
    throw new Error('GeometryCollection is not supported for WKT conversion');
  }

  // Cast to our interface after type validation
  const geom = geometry as GeoJSONGeometry;

  if (!geom.coordinates) {
    throw new Error('Invalid GeoJSON geometry provided - missing coordinates');
  }

  // Apply simplification if requested
  let processedGeom = geom;
  if (simplify) {
    processedGeom = { ...geom };

    switch (geom.type) {
      case 'Polygon':
        processedGeom.coordinates = geom.coordinates.map(ring => simplifyCoordinates(ring, 0.001, aggressive));
        break;
      case 'MultiPolygon':
        processedGeom.coordinates = geom.coordinates.map(polygon =>
          polygon.map(ring => simplifyCoordinates(ring, 0.001, aggressive))
        );
        break;
      case 'LineString':
        processedGeom.coordinates = simplifyCoordinates(geom.coordinates, 0.001, aggressive);
        break;
      case 'MultiLineString':
        processedGeom.coordinates = geom.coordinates.map(line => simplifyCoordinates(line, 0.001, aggressive));
        break;
      // Points don't need simplification
    }
  }

  switch (processedGeom.type) {
    case 'Point':
      return convertPointToWKT(processedGeom.coordinates);

    case 'Polygon':
      return convertPolygonToWKT(processedGeom.coordinates);

    case 'MultiPolygon':
      return convertMultiPolygonToWKT(processedGeom.coordinates);

    case 'LineString':
      return convertLineStringToWKT(processedGeom.coordinates);

    case 'MultiLineString':
      return convertMultiLineStringToWKT(processedGeom.coordinates);

    default:
      throw new Error(`Unsupported geometry type: ${processedGeom.type}`);
  }
}

/**
 * Convert Point coordinates to WKT
 */
function convertPointToWKT(coordinates: number[]): string {
  if (!Array.isArray(coordinates) || coordinates.length < 2) {
    throw new Error('Invalid Point coordinates');
  }
  
  const [lng, lat] = coordinates;
  return `POINT(${lng} ${lat})`;
}

/**
 * Convert Polygon coordinates to WKT
 */
function convertPolygonToWKT(coordinates: number[][][]): string {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid Polygon coordinates');
  }

  // Helper to ensure a ring is closed and valid
  const closeAndValidateRing = (ring: number[][]): number[][] => {
    if (!Array.isArray(ring) || ring.length < 4) {
      throw new Error('Invalid polygon ring - must have at least 4 coordinates');
    }

    // Remove any invalid points
    const cleaned = ring.filter((coord) =>
      Array.isArray(coord) && coord.length >= 2 && isFinite(coord[0]) && isFinite(coord[1])
    );

    if (cleaned.length < 4) {
      throw new Error('Invalid polygon ring after cleaning - not enough valid coordinates');
    }

    const first = cleaned[0];
    const last = cleaned[cleaned.length - 1];
    const isClosed = first[0] === last[0] && first[1] === last[1];
    return isClosed ? cleaned : [...cleaned, [first[0], first[1]]];
  };

  const rings = coordinates.map((ring) => {
    const closed = closeAndValidateRing(ring);
    const ringWKT = closed.map((coord) => `${coord[0]} ${coord[1]}`).join(', ');
    return `(${ringWKT})`;
  });

  return `POLYGON(${rings.join(', ')})`;
}

/**
 * Convert MultiPolygon coordinates to WKT
 */
function convertMultiPolygonToWKT(coordinates: number[][][][]): string {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid MultiPolygon coordinates');
  }

  // Reuse polygon ring closure/validation for each polygon's rings
  const polygons = coordinates.map((polygonCoords) => {
    if (!Array.isArray(polygonCoords) || polygonCoords.length === 0) {
      throw new Error('Invalid polygon in MultiPolygon');
    }

    const rings = polygonCoords.map((ring) => {
      // Close and validate the ring similar to polygon
      const cleaned = ring.filter((coord) => Array.isArray(coord) && coord.length >= 2 && isFinite(coord[0]) && isFinite(coord[1]));
      if (cleaned.length < 4) {
        throw new Error('Invalid ring in MultiPolygon - not enough valid coordinates');
      }
      const first = cleaned[0];
      const last = cleaned[cleaned.length - 1];
      const isClosed = first[0] === last[0] && first[1] === last[1];
      const closed = isClosed ? cleaned : [...cleaned, [first[0], first[1]]];
      const ringWKT = closed.map((coord) => `${coord[0]} ${coord[1]}`).join(', ');
      return `(${ringWKT})`;
    });

    return `(${rings.join(', ')})`;
  });

  return `MULTIPOLYGON(${polygons.join(', ')})`;
}

/**
 * Convert LineString coordinates to WKT
 */
function convertLineStringToWKT(coordinates: number[][]): string {
  if (!Array.isArray(coordinates) || coordinates.length < 2) {
    throw new Error('Invalid LineString coordinates');
  }

  const lineWKT = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
  return `LINESTRING(${lineWKT})`;
}

/**
 * Convert MultiLineString coordinates to WKT
 */
function convertMultiLineStringToWKT(coordinates: number[][][]): string {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid MultiLineString coordinates');
  }

  const lines = coordinates.map(lineCoords => {
    const lineWKT = lineCoords.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
    return `(${lineWKT})`;
  });

  return `MULTILINESTRING(${lines.join(', ')})`;
}

/**
 * Validates that a GeoJSON geometry has valid coordinates
 */
export function validateGeoJSONGeometry(geometry: any): boolean {
  try {
    convertGeoJSONToWKT(geometry);
    return true;
  } catch (error) {
    console.error('Invalid GeoJSON geometry:', error);
    return false;
  }
}

/**
 * Creates a WKT representation from bounds for BBOX clipping
 */
export function createBoundsWKT(bounds: { north: number; south: number; east: number; west: number }): string {
  const { north, south, east, west } = bounds;
  
  // Create a polygon from bounds
  const coordinates = [
    [west, south],  // Bottom-left
    [east, south],  // Bottom-right
    [east, north],  // Top-right
    [west, north],  // Top-left
    [west, south]   // Close the polygon
  ];
  
  const ringWKT = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
  return `POLYGON((${ringWKT}))`;
}

/**
 * Debug function to log WKT conversion details
 */
export function debugWKTConversion(geometry: GeoJSONGeometry): {
  originalType: string;
  coordinatesLength: number;
  wkt: string;
  wktLength: number;
} {
  const wkt = convertGeoJSONToWKT(geometry);
  
  return {
    originalType: geometry.type,
    coordinatesLength: JSON.stringify(geometry.coordinates).length,
    wkt: wkt,
    wktLength: wkt.length
  };
}
