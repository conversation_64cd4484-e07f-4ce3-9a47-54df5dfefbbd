import L from 'leaflet';

const LeafletLegendControl = L.Control.extend({
  options: {
    position: 'bottomleft',
    maxHeight: 600,
    maxVisibleItems: 4, // Show 4 legend cards before scrolling
    className: 'leaflet-legend-control'
  },

  initialize: function(options) {
    L.setOptions(this, options);
    this._legendData = [];
  },

  onAdd: function(map) {
    // Create the main container with existing classes
    this._container = L.DomUtil.create('div', 'map-legend-panel expanded leaflet-control leaflet-bar');

    // Prevent map interaction when interacting with legend
    L.DomEvent.disableClickPropagation(this._container);
    L.DomEvent.disableScrollPropagation(this._container);

    // Create header (matching existing header styling)
    this._header = L.DomUtil.create('div', 'legend-header app-header-blue', this._container);
    this._header.innerHTML = `
      <div class="legend-header-content">
        <h4 class="legend-title mb-0">
          Legend
        </h4>
      </div>
    `;

    // Create scrollable content area (using existing classes)
    this._content = L.DomUtil.create('div', 'legend-content', this._container);

    // Apply existing scrolling styles
    this._content.style.maxHeight = this.options.maxHeight + 'px';
    this._content.style.overflowY = 'auto';

    // Add event delegation for card clicks
    this._container.addEventListener('click', this._handleCardClick.bind(this));

    this._updateContent();
    return this._container;
  },

  onRemove: function(map) {
    if (this._container) {
      this._container.removeEventListener('click', this._handleCardClick.bind(this));
    }
  },

  // Method to update legend data
  updateLegends: function(layersData) {
    this._legendData = layersData || [];
    if (this._content) {
      this._updateContent();
    }
  },



  _updateContent: function() {
    if (!this._content) return;

    // Clear existing content
    this._content.innerHTML = '';

    // Add each legend item using existing card styling
    this._legendData.forEach((layerData, index) => {
      const legendCard = this._createLegendCard(layerData, index);
      this._content.appendChild(legendCard);
    });

    // Update container height based on content
    this._updateContainerHeight();
  },

  _createLegendCard: function(layerData, index) {
    // Create legend card with existing styling
    const card = L.DomUtil.create('div', 'legend-item-card');
    if (layerData.selected) {
      card.classList.add('selected');
    }

    card.innerHTML = `
      <div class="layer-accent-bar ${layerData.selected ? 'selected' : ''}"></div>

      <div class="legend-item-header">
        <div class="layer-title-section">
          <i class="fas fa-map-marked-alt layer-icon"></i>
          <div class="layer-title-container">
            <h6 class="layer-title highlighted-layer-name">${layerData.title || layerData.name}</h6>
          </div>
        </div>
      </div>

      <div class="legend-image-wrapper-enhanced">
        <img
          src="${layerData.legendUrl}"
          alt="Legend for ${layerData.name}"
          class="legend-image-enhanced"
          onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
        />
        <div class="legend-fallback" style="display: none;">
          <div class="fallback-icon">📊</div>
          <div class="fallback-text">No legend available</div>
        </div>
      </div>
    `;

    return card;
  },



  _updateContainerHeight: function() {
    const itemCount = this._legendData.length;
    const maxVisible = this.options.maxVisibleItems;
    
    if (itemCount <= maxVisible) {
      // Show all items without scrolling
      this._content.style.maxHeight = 'none';
      this._container.classList.remove('scrollable');
    } else {
      // Enable scrolling after maxVisible items
      const itemHeight = 140; // Approximate height of each legend card
      const maxHeight = (maxVisible * itemHeight) + 40; // Add padding
      this._content.style.maxHeight = maxHeight + 'px';
      this._container.classList.add('scrollable');
    }
  },



  _handleCardClick: function(e) {
    // Handle card selection if needed
    const card = e.target.closest('.legend-item-card');
    if (card) {
      // Toggle selection
      card.classList.toggle('selected');
      const accentBar = card.querySelector('.layer-accent-bar');
      if (accentBar) {
        accentBar.classList.toggle('selected');
      }
    }
  },

  _generateLayerInfo: function(layerData) {
    const info = [];
    if (layerData.temporal) {
      info.push('Temporal data available');
    }
    if (layerData.queryable) {
      info.push('Click to query features');
    }
    if (layerData.title && layerData.title !== layerData.name) {
      info.push(`Layer: ${layerData.name}`);
    }
    return info.length > 0 ? info.join(' | ') : 'Layer information';
  }
});

// Factory function
L.control.legendControl = function(options) {
  return new LeafletLegendControl(options);
};

export default LeafletLegendControl;
