// Map component for SANSA Flood Monitoring
import React, { useEffect, useState, useCallback, useMemo } from 'react';


import {
  <PERSON><PERSON>ontainer, TileLayer, WMSTileLayer,
  useMap, Popup, Marker, Polygon, LayerGroup
} from 'react-leaflet';
import L from 'leaflet';
import 'leaflet-draw'; // Import leaflet-draw JavaScript
import { fetchAvailableWMSLayers, WMSLayer, fetchFeatureInfo, GetFeatureInfoParams } from '../../services/geoserverService';
import { discoverLayers } from '../../services/discoveryService';
import { fetchFloodRiskData, fetchLayerData } from '../../services/mapService';
import { API_CONFIG } from '../../config';
import { convertGeoJSONToWKT } from '../../utils/wktConverter';

import LeafletLegend from './LeafletLegend';
import LoadingOverlay from './LoadingOverlay';
import FeatureInfoPopup, { FeatureInfo } from './FeatureInfoPopup';
import TemporalSelectionModal from '../AOI/TemporalSelectionModal';
import PinAreaSelectionModal from '../Pin/PinAreaSelectionModal';
import BoundaryHighlightLayer from '../Boundary/BoundaryHighlightLayer';

import AOIMapController from './AOIMapController';
import BoundsCapture from './BoundsCapture';
import MapResizeHandler from './MapResizeHandler';
import MapClickHandler from './MapClickHandler';
import DrawingController from './DrawingController';
import AOIClipMask from './AOIClipMask';
import { applyAOIClipping, applyClippingToParams, detectAndCacheGeometryField } from '../../services/aoiClippingService';

import './MapComponent.css';

// Debug flag for development logging
const DEBUG = process.env.NODE_ENV !== 'production' && (window as any).__MAP_DEBUG__ === true;

interface MapComponentProps {
  selectedLayerNames: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
  selectedTime?: string; // Add selectedTime prop for temporal layers
  onDrawComplete: (layers: any) => void;
  // AOI functionality
  isDrawingMode: boolean;
  onDrawModeChange: (isDrawing: boolean) => void;
  onAOIComplete: (aoiData: any, dateRange: any) => void;
  sidebarCollapsed?: boolean; // Add sidebarCollapsed prop for map resize
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Legend user mode
  legendUserMode?: 'simple' | 'advanced';
  onLegendUserModeChange?: (mode: 'simple' | 'advanced') => void;
  // Coordinate functionality
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;
  // AOI Preview functionality
  onAOIPreview?: (aoiData: any) => void;
  // Boundary highlighting
  highlightedBoundaries?: GeoJSON.Feature[];
  // AOI clipping
  aoiData?: {
    type: 'administrative' | 'drawn' | 'pin' | 'interactive-boundaries';
    level?: 'province' | 'district' | 'municipality' | 'ward';
    name?: string;
    code?: string;
    bounds?: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    area?: number;
    coordinates?: any;
    // Add geometry for precise clipping
    geometry?: GeoJSON.Geometry;
    feature?: GeoJSON.Feature;
    timestamp?: string;
    selectionDetails?: {
      provinceName?: string;
      district?: string;
      municipality?: string;
    };
    // Additional properties for interactive boundaries
    features?: GeoJSON.Feature[];
    count?: number;
  };
  // Layer opacity control
  layerOpacities?: { [layerName: string]: number };
  onOpacityChange?: (layerName: string, opacity: number) => void;
}



const MapRecentre = ({ trigger, center, zoom }: { trigger: boolean, center: [number, number], zoom: number }) => {
  const map = useMap();
  useEffect(() => {
    if (trigger) map.setView(center, zoom);
  }, [trigger, center, zoom, map]);
  return null;
};

// Setup custom panes for AOI clipping
const PaneSetup = () => {
  const map = useMap();
  
  useEffect(() => {
    // Create AOI clip pane with zIndex above tiles but below overlays
    if (!map.getPane('aoiClipPane')) {
      const aoiClipPane = map.createPane('aoiClipPane');
      aoiClipPane.style.zIndex = '450';
      aoiClipPane.style.pointerEvents = 'none';
    }
  }, [map]);
  
  return null;
};









const MapComponent: React.FC<MapComponentProps> = ({
  selectedLayerNames,
  dateRange,
  selectedTime,
  onDrawComplete,
  isDrawingMode,
  onDrawModeChange,
  onAOIComplete,
  sidebarCollapsed,
  selectedBasemap = 'osm:osm',
  onBasemapChange,
  legendUserMode = 'simple',
  isCoordinatePinMode = false,
  onCoordinateSelected,
  onLegendUserModeChange,
  onAOIPreview,
  highlightedBoundaries = [],
  aoiData,
  layerOpacities: externalLayerOpacities,
  onOpacityChange: externalOnOpacityChange
}) => {

  // Track AOI data changes for component state management
  useEffect(() => {
    // Component logic for AOI data handling without debug output
  }, [aoiData, selectedLayerNames.length, dateRange, selectedTime]);



  // Define hasValidAOI at component level for use throughout the component
  const hasValidAOI = aoiData && (aoiData.geometry || aoiData.bounds);

  const [center] = useState<[number, number]>([-29.0, 24.0]);
  const [zoom] = useState<number>(6);
  const [wmsLayers, setWmsLayers] = useState<WMSLayer[]>([]);
  const [mapBounds, setMapBounds] = useState<any>(null);
  const [loadingLayers, setLoadingLayers] = useState<{ [key: string]: boolean }>({});
  const [layerProgress, setLayerProgress] = useState<{ [key: string]: number }>({});
  const [tileLoadingState, setTileLoadingState] = useState<{ [key: string]: { loaded: number; total: number } }>({});
  const [floodRiskAreas, setFloodRiskAreas] = useState<any[]>([]);
  const [dwsVillages, setDwsVillages] = useState<any[]>([]);
  const [activePopup, setActivePopup] = useState<string | null>(null);
  const [errorLayers, setErrorLayers] = useState<{ [key: string]: string }>({});
  const [pinCoordinates, setPinCoordinates] = useState<{ lat: number, lng: number } | null>(null);
  const [showPinAreaModal, setShowPinAreaModal] = useState(false);

  // Compute if any raster layers are active for efficient AOI clipping
  const isRasterActive = useMemo(() => {
    return selectedLayerNames.some(layerName =>
      /mosaic|imagery|satellite|tiff|raster|rgb|infrared|coverage|landsat|sentinel|modis|dem|elevation/i.test(layerName)
    );
  }, [selectedLayerNames]);



  // Reset pin coordinates when exiting coordinate pin mode
  useEffect(() => {
    if (!isCoordinatePinMode) {
      setPinCoordinates(null);
      setShowPinAreaModal(false);
    }
  }, [isCoordinatePinMode]);

  const [internalLayerOpacities, setInternalLayerOpacities] = useState<{ [layerName: string]: number }>({});

  // Use external opacity state if provided, otherwise use internal state
  const layerOpacities = externalLayerOpacities || internalLayerOpacities;
  // Cache of geometry fields per layer
  const [geometryFields, setGeometryFields] = useState<Record<string, string>>({});



  // Pre-fetch geometry fields for selected layers to avoid await in render
  useEffect(() => {
    console.log(`Fetching geometry fields for selected layers:`, selectedLayerNames);
    let isCancelled = false;
    (async () => {
      const entries: Record<string, string> = {};
      for (const layerName of selectedLayerNames) {
        try {
          console.log(`Fetching geometry field for layer: ${layerName}`);
          const geometryField = await detectAndCacheGeometryField(layerName);
          entries[layerName] = geometryField;
          console.log(` Found geometry field for ${layerName}: ${geometryField}`);
        } catch (e) {
          console.warn(`❌ Failed to get geometry field for layer ${layerName}:`, e);
          entries[layerName] = 'geometry'; // Fallback
        }
      }
      if (!isCancelled) {
        console.log(`Updating geometry fields cache with ${Object.keys(entries).length} entries:`, entries);
        setGeometryFields(entries);
        console.log(`Geometry field cache now contains:`, Object.keys(entries));
      }
    })();
    return () => { isCancelled = true; };
  }, [selectedLayerNames]);

  // Pre-fetch geometry fields for all available layers when AOI changes
  // This ensures geometry fields are available when layers are toggled after AOI selection
  // Also handles AOI changes (province to district, etc.) to maintain stability
  useEffect(() => {
    if (hasValidAOI && wmsLayers.length > 0) {
      console.log(`AOI ACTIVE - Pre-fetching geometry fields for ALL ${wmsLayers.length} available layers`);
      console.log(`Current geometry field cache has ${Object.keys(geometryFields).length} entries`);
      let isCancelled = false;

      (async () => {
        const entries: Record<string, string> = { ...geometryFields }; // Start with existing cache

        // Check if we need to fetch any geometry fields for ALL layers (not just selected ones)
        const layersNeedingGeometryFields = wmsLayers.filter(layer => !entries[layer.name]);
        console.log(`Layers needing geometry field detection: ${layersNeedingGeometryFields.length}/${wmsLayers.length}`);

        if (layersNeedingGeometryFields.length === 0) {
          console.log(`All geometry fields already cached, no need to fetch`);
          return;
        }

        console.log(`Need to fetch geometry fields for ${layersNeedingGeometryFields.length} layers:`,
          layersNeedingGeometryFields.map(l => l.name));

        // Throttle requests to prevent resource exhaustion - process layers in small batches
        const batchSize = 3; // Process 3 layers at a time
        const delay = 500; // 500ms delay between batches

        for (let i = 0; i < layersNeedingGeometryFields.length; i += batchSize) {
          if (isCancelled) break;

          const batch = layersNeedingGeometryFields.slice(i, i + batchSize);
          console.log(`Processing geometry field batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(layersNeedingGeometryFields.length/batchSize)}`);

          // Process batch in parallel
          await Promise.all(batch.map(async (layer) => {

            try {
              console.log(`Pre-fetching geometry field for layer: ${layer.name}`);
              const geometryField = await detectAndCacheGeometryField(layer.name);
              entries[layer.name] = geometryField;
              console.log(` Pre-fetched geometry field for ${layer.name}: ${geometryField}`);
            } catch (e) {
              console.warn(`❌ Failed to pre-fetch geometry field for layer ${layer.name}:`, e);
              entries[layer.name] = 'geometry'; // Fallback
            }
          }));

          // Delay between batches to prevent overwhelming the server
          if (i + batchSize < layersNeedingGeometryFields.length && !isCancelled) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!isCancelled) {
          console.log(`Updating geometry fields cache with pre-fetched data:`, entries);
          setGeometryFields(entries);
        }
      })();

      return () => { isCancelled = true; };
    }
  }, [hasValidAOI, wmsLayers, aoiData?.feature?.id, aoiData?.bounds, aoiData?.geometry?.type]);

  // Update debug functions when geometryFields changes
  useEffect(() => {
    if (DEBUG) {
      // Update the global debug functions to include geometryFields
      (window as any).debugAOIClipping = () => {
        console.log('AOI Clipping Debug Report:');
        console.log('AOI Data:', aoiData);
        console.log('Date Range:', dateRange);
        console.log('Selected Layers:', selectedLayerNames);
        console.log('Selected Time:', selectedTime);
        console.log('Geometry Fields Cache:', geometryFields);

        if (aoiData) {
          console.log('AOI clipping should be active');
          if (aoiData.geometry) {
            console.log('CQL filtering available');
            console.log('Geometry type:', aoiData.geometry.type);

            // CQL testing removed to prevent resource exhaustion
            console.log(`CQL testing disabled - geometry fields available for ${selectedLayerNames.length} layers`);
          } else if (aoiData.bounds) {
            console.log('⚠️ Only BBOX filtering available');
          }
        } else {
          console.log('No AOI clipping - layers will render at full extent');
          console.log('This is normal behavior when no AOI is selected');
        }
      };

      // Add function to manually test a URL
      (window as any).testWMSUrl = async (url: string) => {
        console.log(`Testing WMS URL: ${url.substring(0, 100)}...`);
        try {
          const response = await fetch(url);
          if (response.ok) {
            console.log(` URL test PASSED: ${response.status}`);
            return true;
          } else {
            console.error(`❌ URL test FAILED: ${response.status}`);
            const errorText = await response.text();
            console.error(`Error response:`, errorText);
            return false;
          }
        } catch (error) {
          console.error(`❌ URL test ERROR:`, error);
          return false;
        }
      };
    }

    return () => {
      if (DEBUG) {
        delete (window as any).debugAOIClipping;
        delete (window as any).testWMSUrl;
      }
    };
  }, [aoiData, dateRange, selectedLayerNames, selectedTime, geometryFields]);

  // CQL testing function removed to prevent resource exhaustion

  // Legend selection state
  const [selectedLegendLayer, setSelectedLegendLayer] = useState<string | undefined>();

  // Auto-select the last selected layer when layers change
  useEffect(() => {

    // Check for exact matches (for debugging purposes)
    wmsLayers.filter(wmsLayer =>
      selectedLayerNames.includes(wmsLayer.name)
    );

    // Run comprehensive debug summary (only when layers actually change)
    if (selectedLayerNames.length > 0 && wmsLayers.length > 0) {
      debugLayerMatching();
    }

    if (selectedLayerNames.length > 0 && !selectedLegendLayer) {
      // Select the last layer in the list (most recently added)
      setSelectedLegendLayer(selectedLayerNames[selectedLayerNames.length - 1]);
    } else if (selectedLayerNames.length === 0) {
      // Clear selection if no layers are visible
      setSelectedLegendLayer(undefined);
    } else if (selectedLegendLayer && !selectedLayerNames.includes(selectedLegendLayer)) {
      // If selected layer is no longer visible, select the last visible layer
      setSelectedLegendLayer(selectedLayerNames[selectedLayerNames.length - 1]);
    }
  }, [selectedLayerNames, wmsLayers]); // Removed selectedLegendLayer and debugLayerMatching from dependencies

  // Handle manual legend layer selection
  const handleLegendLayerSelect = (layerName: string) => {
    setSelectedLegendLayer(layerName);
  };

  // Feature info popup state
  const [featureInfo, setFeatureInfo] = useState<FeatureInfo | null>(null);

  // AOI state management
  const [drawnPolygon, setDrawnPolygon] = useState<any>(null);
  const [showTemporalModal, setShowTemporalModal] = useState(false);

  // Tile error tracking to prevent infinite retries
  const [, setFailedTiles] = useState<Set<string>>(new Set());
  const [, setLayerErrorCounts] = useState<Record<string, number>>({});
  const [disabledLayers, setDisabledLayers] = useState<Set<string>>(new Set());

  // Track last toggled layer for expanded legend (now using selectedLegendLayer)
  const [, setLastToggledLayer] = useState<WMSLayer | null>(null);

  // Update lastToggledLayer when selectedLegendLayer changes
  useEffect(() => {
    if (selectedLegendLayer) {
      const selectedLayer = wmsLayers.find(layer => layer.name === selectedLegendLayer);
      if (selectedLayer) {
        setLastToggledLayer(selectedLayer);
      }
    }
  }, [selectedLegendLayer, wmsLayers]);
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });



  // Removed isSentinelLayer - using dynamic layer properties instead

  // Get default bounds for South Africa when no AOI is selected
  const getDefaultBounds = useCallback((): [[number, number], [number, number]] => {
    // South Africa bounding box
    return [
      [-34.8, 16.3], // Southwest corner [lat, lng]
      [-22.1, 32.9]  // Northeast corner [lat, lng]
    ];
  }, []);

  const getLayerBounds = useCallback((bbox: any) => {
    if (!bbox) {
      // Return default South Africa bounds when layer has no bbox
      console.log('No layer bbox available, using default South Africa bounds');
      return getDefaultBounds();
    }

    const { miny, minx, maxy, maxx } = bbox;

    // Convert to numbers and validate
    const minY = Number(miny);
    const minX = Number(minx);
    const maxY = Number(maxy);
    const maxX = Number(maxx);

    // Check for invalid coordinates
    if (isNaN(minY) || isNaN(minX) || isNaN(maxY) || isNaN(maxX)) {
      console.warn('Invalid bbox coordinates detected, using default bounds:', { miny, minx, maxy, maxx });
      return getDefaultBounds();
    }

    // Validate coordinate ranges
    if (minY < -90 || minY > 90 || maxY < -90 || maxY > 90 ||
        minX < -180 || minX > 180 || maxX < -180 || maxX > 180) {
      console.warn('Bbox coordinates out of valid range, using default bounds:', { minY, minX, maxY, maxX });
      return getDefaultBounds();
    }

    // Ensure min < max
    if (minY >= maxY || minX >= maxX) {
      console.warn('Invalid bbox: min >= max, using default bounds:', { minY, minX, maxY, maxX });
      return getDefaultBounds();
    }

    return [[minY, minX], [maxY, maxX]] as [[number, number], [number, number]];
  }, [getDefaultBounds]);

  // Calculate tight bounding box from AOI geometry coordinates
  const calculateTightBounds = useCallback((geometry: any) => {
    let minLat = Infinity, maxLat = -Infinity;
    let minLng = Infinity, maxLng = -Infinity;
    
    const processCoordinates = (coords: any) => {
      if (Array.isArray(coords)) {
        if (typeof coords[0] === 'number' && typeof coords[1] === 'number') {
          // This is a coordinate pair [lng, lat]
          const lng = coords[0];
          const lat = coords[1];
          minLat = Math.min(minLat, lat);
          maxLat = Math.max(maxLat, lat);
          minLng = Math.min(minLng, lng);
          maxLng = Math.max(maxLng, lng);
        } else {
          // This is an array of coordinates, process recursively
          coords.forEach(processCoordinates);
        }
      }
    };
    
    try {
      if (geometry?.coordinates) {
        processCoordinates(geometry.coordinates);
        
        // Add small buffer to ensure complete containment
        const latBuffer = (maxLat - minLat) * 0.01; // 1% buffer
        const lngBuffer = (maxLng - minLng) * 0.01; // 1% buffer
        
        return {
          north: maxLat + latBuffer,
          south: minLat - latBuffer,
          east: maxLng + lngBuffer,
          west: minLng - lngBuffer
        };
      }
    } catch (error) {
      console.warn('Error calculating tight bounds:', error);
    }
    
    return null;
  }, []);

  const isLayerVisible = useCallback((layer: WMSLayer) => {
    if (!layer || !layer.name) {
      return false;
    }

    // Simple check: layer is visible if it's in the selected layers array
    // Some layer names might include namespace prefixes that need to be stripped
    const layerBaseName = layer.name.includes(':') ? layer.name.split(':')[1] : layer.name;
    const isVisible = selectedLayerNames.includes(layer.name) || selectedLayerNames.includes(layerBaseName);

    return isVisible;
  }, [selectedLayerNames]);





  // Cleanup opacity state when layers are removed (only for internal state)
  useEffect(() => {
    const visibleLayerNames = wmsLayers.filter(isLayerVisible).map(layer => layer.name);

    if (!externalLayerOpacities) {
      setInternalLayerOpacities(prev => {
        const cleaned = { ...prev };
        Object.keys(cleaned).forEach(layerName => {
          if (!visibleLayerNames.includes(layerName)) {
            delete cleaned[layerName];
          }
        });
        return cleaned;
      });
    }

    // Clear error tracking for layers that are no longer visible
    setLayerErrorCounts(prev => {
      const newCounts = { ...prev };
      Object.keys(newCounts).forEach(layerName => {
        if (!visibleLayerNames.includes(layerName)) {
          delete newCounts[layerName];
        }
      });
      return newCounts;
    });

    // Clear failed tiles for layers that are no longer visible
    setFailedTiles(prev => {
      const newSet = new Set<string>();
      prev.forEach(tileKey => {
        const layerName = tileKey.split('-')[0];
        if (visibleLayerNames.includes(layerName)) {
          newSet.add(tileKey);
        }
      });
      return newSet;
    });
  }, [wmsLayers]); // Removed isLayerVisible from dependencies to prevent re-renders

  useEffect(() => {
    const loadAllLayers = async () => {
      try {
        // Load layers from discovery service (includes both local and remote)
        const discoveryResult = await discoverLayers();
        const allLayers = discoveryResult.layers || [];

        // Convert discovery layers to WMSLayer format for compatibility
        const wmsCompatibleLayers = allLayers.map(layer => {
          // Handle different bbox formats
          let bbox;
          const layerBbox = (layer as any).bbox;

          if (layerBbox && Array.isArray(layerBbox) && layerBbox.length === 4) {
            // Array format: [minx, miny, maxx, maxy]
            bbox = {
              minx: layerBbox[0],
              miny: layerBbox[1],
              maxx: layerBbox[2],
              maxy: layerBbox[3]
            };
          } else if (layerBbox && typeof layerBbox === 'object') {
            // Object format: {minx, miny, maxx, maxy}
            bbox = {
              minx: layerBbox.minx || layerBbox[0] || -180,
              miny: layerBbox.miny || layerBbox[1] || -90,
              maxx: layerBbox.maxx || layerBbox[2] || 180,
              maxy: layerBbox.maxy || layerBbox[3] || 90
            };
          } else {
            // Default global bounds
            bbox = {
              minx: -180,
              miny: -90,
              maxx: 180,
              maxy: 90
            };
          }

          // Validate bbox values
          if (isNaN(bbox.minx) || isNaN(bbox.miny) || isNaN(bbox.maxx) || isNaN(bbox.maxy)) {
            console.warn(`Invalid bbox for layer ${layer.name}, using global bounds`);
            bbox = { minx: -180, miny: -90, maxx: 180, maxy: 90 };
          }

          return {
            name: layer.name,
            title: layer.title || layer.name,
            type: 'raster',
            queryable: (layer as any).queryable || false,
            bbox: bbox,
            // Add remote layer properties
            isRemote: (layer as any).isRemote || false,
            serviceType: (layer as any).serviceType,
            remoteUrl: (layer as any).remoteUrl,
            url: (layer as any).url
          };
        });


        setWmsLayers(wmsCompatibleLayers as WMSLayer[]);
      } catch (error) {
        console.error('MapComponent: Failed to load layers from discovery:', error);

        // Fallback to local WMS layers only
        try {
          const localLayers = await fetchAvailableWMSLayers();
          setWmsLayers(localLayers);
        } catch (fallbackError) {
          console.error('MapComponent: Fallback also failed:', fallbackError);
          setWmsLayers([]);
        }
      }
    };
    loadAllLayers();
  }, []);
  //Use this for the default loading layers
  useEffect(() => {
    if (selectedLayerNames.includes('floodRisk') && mapBounds) {
      fetchFloodRiskData(mapBounds, dateRange)
        .then(setFloodRiskAreas)
        .catch(console.error);
    } else {
      setFloodRiskAreas([]);
    }
  }, [selectedLayerNames, dateRange, mapBounds]);

  useEffect(() => {
    if (selectedLayerNames.includes('dwsVillage') && mapBounds) {
      fetchLayerData('dwsVillage', mapBounds)
        .then(setDwsVillages)
        .catch(console.error);
    } else {
      setDwsVillages([]);
    }
  }, [selectedLayerNames, mapBounds]);

  // Track the last toggled layer for expanded legend
  useEffect(() => {

    const visibleLayers = wmsLayers.filter(layer => selectedLayerNames.includes(layer.name));
    if (visibleLayers.length > 0) {
      // Set the last layer in the visible layers array as the last toggled
      setLastToggledLayer(visibleLayers[visibleLayers.length - 1]);
    } else {
      setLastToggledLayer(null);
    }
  }, [selectedLayerNames, wmsLayers]);

  const handleCreated = useCallback((e: any) => {
    if (e.layerType === 'polygon') {
      const geoJSON = e.layer.toGeoJSON();

      // If in AOI drawing mode, start the AOI workflow
      if (isDrawingMode) {
        setDrawnPolygon(geoJSON);
        onDrawModeChange(false); // Exit drawing mode
        setShowTemporalModal(true); // Show temporal selection modal
      } else {
        // Regular drawing functionality
        onDrawComplete(geoJSON);
      }
    }
  }, [onDrawComplete, isDrawingMode, onDrawModeChange]);

  // Handle feature info click
  const handleFeatureInfoClick = useCallback(async (event: any, queryableLayers: WMSLayer[]) => {
    const { latlng, containerPoint } = event;

    // Set popup position based on click coordinates
    setPopupPosition({ x: containerPoint.x + 10, y: containerPoint.y - 10 });

    try {
      // For now, query the first queryable layer
      // TODO: In the future, we could query multiple layers and combine results
      const layer = queryableLayers[0];

      // Get map dimensions
      const mapSize = event.target.getSize();
      const bounds = event.target.getBounds();

      const params: GetFeatureInfoParams = {
        layers: layer.name,
        queryLayers: layer.name,
        x: Math.round(containerPoint.x),
        y: Math.round(containerPoint.y),
        width: mapSize.x,
        height: mapSize.y,
        bbox: `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`,
        srs: 'EPSG:4326',
        infoFormat: 'application/json',
        featureCount: 10
      };

      const response = await fetchFeatureInfo(params);

      // Parse GeoServer response (format may vary)
      let features = [];
      if (response && response.features) {
        features = response.features;
      } else if (response && Array.isArray(response)) {
        features = response;
      } else if (response && typeof response === 'object') {
        // Handle other response formats
        features = [{ properties: response }];
      }

      if (features.length > 0) {
        setFeatureInfo({
          layerName: layer.title || layer.name,
          features: features,
          coordinates: {
            lat: latlng.lat,
            lng: latlng.lng
          }
        });
      }
    } catch (error) {
      console.error('Error fetching feature info:', error);
      // Could show an error message to user here
    }
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'red';
      case 'moderate': return 'yellow';
      case 'low': return 'green';
      default: return 'blue';
    }
  };

  const handleMarkerClick = useCallback((layerName: string) => {
    setActivePopup(prev => prev === layerName ? null : layerName);
  }, []);

  const handleTileLoadStart = useCallback((layerName: string) => {
    setTileLoadingState(prevState => {
      const current = prevState[layerName] || { loaded: 0, total: 0 };
      return {
        ...prevState,
        [layerName]: { ...current, total: current.total + 1 }
      };
    });
  }, []);

  const handleTileLoad = useCallback((layerName: string) => {
    setTileLoadingState(prevState => {
      const current = prevState[layerName] || { loaded: 0, total: 0 };
      const newState = {
        ...prevState,
        [layerName]: { ...current, loaded: current.loaded + 1 }
      };

      // Calculate and update progress based on actual tile loading
      if (current.total > 0) {
        const newLoaded = current.loaded + 1;
        const progress = Math.round((newLoaded / current.total) * 100);
        setLayerProgress(prevProgress => ({
          ...prevProgress,
          [layerName]: Math.min(progress, 100)
        }));
      }

      return newState;
    });
  }, []);

  const handleLayerLoading = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: true }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 0 }));
    // Reset tile loading state when starting to load
    setTileLoadingState(prev => ({ ...prev, [layerName]: { loaded: 0, total: 0 } }));
  }, []);

  const handleLayerLoad = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 100 }));
    setErrorLayers(prev => {
      const updated = { ...prev };
      delete updated[layerName];
      return updated;
    });

    // Clear the completed loading state after a short delay to prevent lingering
    setTimeout(() => {
      setLayerProgress(prev => {
        const updated = { ...prev };
        delete updated[layerName];
        return updated;
      });
      setTileLoadingState(prev => {
        const updated = { ...prev };
        delete updated[layerName];
        return updated;
      });
    }, 2000);
  }, []);

  const handleLayerError = useCallback((layerName: string, error: any) => {
    // Increment error count for this layer
    setLayerErrorCounts(prev => {
      const newCount = (prev[layerName] || 0) + 1;
      const updatedCounts = { ...prev, [layerName]: newCount };

      // If this layer has had too many errors, disable it and stop retries
      if (newCount >= 10) {
        console.error(`Layer "${layerName}" has failed ${newCount} times - disabling to prevent infinite loops`);
        setDisabledLayers(prevDisabled => new Set(prevDisabled).add(layerName));
        setErrorLayers(prev => ({ ...prev, [layerName]: 'Layer disabled due to repeated failures' }));
        return updatedCounts;
      }

      // If this layer has had too many errors, stop reporting to prevent spam
      if (newCount > 5) {
        console.warn(`⚠️ Layer "${layerName}" has failed ${newCount} times - throttling error reporting`);
        return updatedCounts;
      }

      console.error(`❌ Layer error for "${layerName}" (attempt ${newCount}):`, error);
      return updatedCounts;
    });

    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setErrorLayers(prev => ({ ...prev, [layerName]: error.message || 'Failed to load layer' }));
  }, []);

  // Debug summary function


  // Get the time parameter for temporal layers
  const getTimeParameter = useCallback(() => {
    // Use selectedTime if provided, otherwise use date range
    if (selectedTime) {
      return selectedTime;
    }

    if (!dateRange.startDate || !dateRange.endDate) {
      return undefined;
    }

    // Convert to ISO 8601 format for WMS TIME parameter
    const startISO = new Date(dateRange.startDate).toISOString().split('T')[0];
    const endISO = new Date(dateRange.endDate).toISOString().split('T')[0];

    return `${startISO}/${endISO}`;
  }, [selectedTime, dateRange]);

  // Effect to refresh layers when date range changes (Phase 3 implementation)
  useEffect(() => {
    // Calculate time parameter directly to avoid dependency issues
    let timeParam: string | undefined;
    if (selectedTime) {
      timeParam = selectedTime;
    } else if (dateRange.startDate && dateRange.endDate) {
      const startISO = new Date(dateRange.startDate).toISOString().split('T')[0];
      const endISO = new Date(dateRange.endDate).toISOString().split('T')[0];
      timeParam = `${startISO}/${endISO}`;
    }

    if (timeParam && selectedLayerNames.length > 0) {
      console.log(`Date range changed, refreshing temporal layers with TIME: ${timeParam}`);

      // Force layer refresh by clearing and reloading layer progress
      setLayerProgress(prev => {
        const newProgress = { ...prev };
        selectedLayerNames.forEach(layerName => {
          newProgress[layerName] = 0;
        });
        return newProgress;
      });
    }
  }, [dateRange, selectedTime, selectedLayerNames]);

  const debugLayerMatching = useCallback(() => {
    const baseWmsUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;

    // Show example URLs for selected layers
    const visibleLayers = wmsLayers.filter(layer => isLayerVisible(layer));
    if (visibleLayers.length > 0) {
      visibleLayers.forEach(layer => {
        const exampleParams = new URLSearchParams({
          SERVICE: 'WMS',
          VERSION: '1.1.1',
          REQUEST: 'GetMap',
          LAYERS: layer.name,
          STYLES: '',
          FORMAT: 'image/png',
          TRANSPARENT: 'true',
          SRS: 'EPSG:4326',
          BBOX: '-180,-90,180,90',
          WIDTH: '256',
          HEIGHT: '256'
        });
        const fullUrl = `${baseWmsUrl}?${exampleParams.toString()}`;
        console.log(`  ${layer.name}: ${fullUrl}`);
      });
    }
  }, [selectedLayerNames, wmsLayers, isLayerVisible, errorLayers, loadingLayers]);

  // Function to test WMS URL accessibility (call from console)
  const testWmsUrl = useCallback(async (layerName: string) => {
    const baseUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
    const testParams = new URLSearchParams({
      SERVICE: 'WMS',
      VERSION: '1.1.1',
      REQUEST: 'GetMap',
      LAYERS: layerName,
      STYLES: '',
      FORMAT: 'image/png',
      TRANSPARENT: 'true',
      SRS: 'EPSG:4326',
      BBOX: '-180,-90,180,90',
      WIDTH: '256',
      HEIGHT: '256'
    });
    const testUrl = `${baseUrl}?${testParams.toString()}`;


    try {
      const response = await fetch(testUrl);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(` WMS Error Response:`, errorText);
      }
    } catch (error) {
      console.error(` WMS URL test failed:`, error);
    }
  }, []);

  // Make debug functions available globally for console debugging in development
  useEffect(() => {
    if (DEBUG) {
      (window as any).testWmsUrl = testWmsUrl;
      (window as any).debugLayerMatching = debugLayerMatching;
      // Initialize AOI debugger
      console.log('AOI Debugging tools available:');
      console.log('- window.aoiDebugger.runFullTest() - Run comprehensive tests');
      console.log('- window.aoiDebugger.testCurrentAOI() - Test current AOI state');
      console.log('- window.testWmsUrl(layerName) - Test WMS layer accessibility');
      console.log('- window.debugLayerMatching() - Debug layer matching');
      console.log('- window.testFullExtentMode() - Test layer rendering without AOI');

      // Add test function for full extent mode
      (window as any).testFullExtentMode = () => {
        console.log('Testing Full Extent Mode:');
        console.log('Current AOI Data:', aoiData);
        console.log('Selected Layers:', selectedLayerNames);
        console.log('Expected behavior: Layers should render at full extent without clipping');

        if (aoiData) {
          console.log('⚠️ AOI is currently active - to test full extent mode, clear AOI selection');
        } else {
          console.log('No AOI active - full extent mode should be working');
          console.log('Try toggling layers to see full extent rendering');
        }
      };
    }

    return () => {
      if (DEBUG) {
        delete (window as any).testWmsUrl;
        delete (window as any).debugLayerMatching;
        delete (window as any).testFullExtentMode;
      }
    };
  }, [testWmsUrl, debugLayerMatching]);

  const handleCloseLoader = useCallback(() => {
    setLoadingLayers({});
    setErrorLayers({});
    setLayerProgress({});
  }, []);

  // AOI workflow handlers
  const handleTemporalConfirm = useCallback((dateRange: { startDate: string; endDate: string }) => {
    setShowTemporalModal(false);

    // Instead of showing modal, call the AOI preview callback
    if (onAOIPreview && drawnPolygon) {
      const coordinates = drawnPolygon.geometry.coordinates[0];
      const lats = coordinates.map((coord: number[]) => coord[1]);
      const lngs = coordinates.map((coord: number[]) => coord[0]);

      const bounds = {
        north: Math.max(...lats),
        south: Math.min(...lats),
        east: Math.max(...lngs),
        west: Math.min(...lngs)
      };

      // Calculate area (simple bounding box area)
      const area = Math.abs((bounds.east - bounds.west) * (bounds.north - bounds.south)) * 111 * 111;

      const aoiData = {
        type: 'drawn',
        coordinates: drawnPolygon,
        bounds,
        area,
        dateRange
      };

      onAOIPreview(aoiData);
    }
  }, [drawnPolygon, onAOIPreview]);

  // Pin area selection handlers
  const handlePinPlaced = useCallback((latlng: { lat: number, lng: number }) => {
    console.log('Pin placed at:', latlng);
    setShowPinAreaModal(true);
  }, []);

  const handlePinAreaSelected = useCallback((areaConfig: any) => {
    console.log('Pin area selected:', areaConfig);
    setShowPinAreaModal(false);

    // Create AOI data similar to drawn polygon
    if (onAOIPreview) {
      const aoiData = {
        type: 'pin',
        name: `Pin Area (${areaConfig.size} km²)`,
        coordinates: areaConfig.coordinates,
        bounds: areaConfig.bounds,
        area: areaConfig.area,
        shape: areaConfig.shape,
        dateRange: null // Will be handled in temporal selection if needed
      };

      onAOIPreview(aoiData);
    }
  }, [onAOIPreview]);

  return (
    <div className="map-wrapper">
      <MapContainer
        center={center}
        zoom={zoom}
        scrollWheelZoom
        className={`map ${isDrawingMode ? 'drawing-mode' : ''} ${isCoordinatePinMode ? 'pin-mode' : ''}`}
        worldCopyJump
        maxBoundsViscosity={1.0}
      >
        <MapRecentre trigger={selectedLayerNames.length === 0 && !hasValidAOI} center={center} zoom={zoom} />
        
        {/* Setup custom panes */}
        <PaneSetup />
        
        {/* Dynamic Basemap Layer */}
        {selectedBasemap === 'osm:osm' ? (
          <TileLayer
            attribution='&copy; OpenStreetMap contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
        ) : (
          <WMSTileLayer
            key={`basemap-${selectedBasemap}`}
            url={`${API_CONFIG.BASE_URL}/ows/wms-proxy`}
            layers={selectedBasemap}
            format="image/jpeg"
            transparent={false}
            version="1.1.1"
            attribution="GeoServer Basemap"
            opacity={1}
            maxZoom={19}
            minZoom={2}
            tileSize={256}
          />
        )}
        <LayerGroup>
          {/* Show warnings for selected layers that can't be found */}
          {selectedLayerNames.length > 0 && (
            <div className="layer-warnings" style={{
              position: 'absolute',
              zIndex: 1000,
              top: 10,
              right: 10,
              maxWidth: '300px',
              pointerEvents: 'none'
            }}>
              {selectedLayerNames.filter(name => !wmsLayers.some(layer =>
                layer.name === name || (layer.name.includes(':') && layer.name.split(':')[1] === name)
              )).map(name => (
                <div key={`warning-${name}`} style={{
                  backgroundColor: 'rgba(255, 193, 7, 0.8)',
                  color: '#212529',
                  padding: '5px 10px',
                  borderRadius: '4px',
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  Layer not found: {name}
                </div>
              ))}
              {/* Show warnings for disabled layers */}
              {selectedLayerNames.filter(name => disabledLayers.has(name)).map(name => (
                <div key={`disabled-${name}`} style={{
                  backgroundColor: 'rgba(220, 53, 69, 0.8)',
                  color: '#fff',
                  padding: '5px 10px',
                  borderRadius: '4px',
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  Layer "{name}" disabled (too many errors)
                </div>
              ))}
            </div>
          )}
          {wmsLayers.filter(layer => {
            const visible = isLayerVisible(layer);
            const disabled = disabledLayers.has(layer.name);

            if (disabled) {
              return false;
            }

            return visible;
          }).map(layer => {
            const isVisible = isLayerVisible(layer);

            if (!isVisible) return null;

            const bounds = getLayerBounds(layer.bbox);
            console.log(`Layer "${layer.name}" bounds:`, bounds);

            // Skip layer if bounds are invalid
            if (bounds && bounds.some(coord => coord.some(val => isNaN(val)))) {
              console.error(`❌ Skipping layer "${layer.name}" due to invalid bounds:`, bounds);
              return null;
            }

            // Check if this is a remote layer
            const isRemoteLayer = (layer as any).isRemote;

            // Dynamic format detection based on layer properties
            const format = (layer as any).formats?.includes('image/png') ? 'image/jpeg' : 'image/png';
            const transparent = format === 'image/png';

            // Determine the appropriate URL based on layer type
            let baseLayerUrl: string;
            let layerName: string;

            // Always use the WMS proxy for both local and remote layers
            // The proxy will handle the conversion for remote layers
            baseLayerUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
            layerName = layer.name;

            // Get temporal parameter for this layer
            const timeParam = getTimeParameter();

            // Enhanced temporal parameter debugging
            const isTemporalLayer = layer.temporal || (layer as any).isTemporal;
            if (isTemporalLayer) {
              console.log(`Temporal layer "${layer.name}":`, {
                hasTimeParam: !!timeParam,
                timeParam: timeParam,
                dateRange: dateRange,
                selectedTime: selectedTime,
                layerTemporal: layer.temporal
              });
            } else if (timeParam) {
              console.log(`⚠️ Non-temporal layer "${layer.name}" has time parameter: ${timeParam}`);
            }

            // Build final layer URL with parameters
            let layerUrl = baseLayerUrl;

            // Add temporal and AOI clipping parameters
            const urlParams = new URLSearchParams();

            // Add essential WMS parameters to prevent "No service" errors
            urlParams.set('SERVICE', 'WMS');
            urlParams.set('REQUEST', 'GetMap');
            urlParams.set('LAYERS', layerName);
            urlParams.set('FORMAT', format);
            urlParams.set('TRANSPARENT', transparent.toString());
            urlParams.set('VERSION', '1.1.1');

            if (timeParam && isTemporalLayer) {
              urlParams.set('TIME', timeParam);
              console.log(`Adding TIME parameter for temporal layer "${layer.name}": ${timeParam}`);
            }

            // Unified AOI clipping: Apply only if AOI data exists and is valid
            if (hasValidAOI) {
              console.log(`🔧 Applying unified AOI clipping to layer "${layer.name}":`, {
                hasGeometry: !!aoiData.geometry,
                hasFeature: !!aoiData.feature,
                hasBounds: !!aoiData.bounds,
                aoiType: aoiData?.type || 'unknown'
              });

              try {
                // Use unified clipping service (now synchronous)
                const clippingResult = applyAOIClipping({
                  geometry: aoiData.geometry,
                  bounds: aoiData.bounds,
                  layerName: layer.name,
                  geometryField: geometryFields[layer.name],
                  maxWKTLength: 6000,
                  enableFallback: true
                });

                if (clippingResult.success) {
                  // Apply clipping parameters to URL
                  applyClippingToParams(urlParams, clippingResult);

                  console.log(`✅ ${clippingResult.method} clipping applied to "${layer.name}":`, clippingResult.parameters);

                  if (clippingResult.geometryField) {
                    console.log(`Using geometry field: ${clippingResult.geometryField}`);
                  }
                  if (clippingResult.wktLength) {
                    console.log(`WKT length: ${clippingResult.wktLength} characters`);
                  }
                } else {
                  console.warn(`⚠️ Clipping failed for "${layer.name}": ${clippingResult.error}`);
                  console.log(`Layer will render at full extent`);
                }

              } catch (error) {
                console.error(`❌ Unified clipping service error for "${layer.name}":`, error);
                console.log(`Layer will render at full extent`);
              }
            } else {
              // No AOI clipping - render full layer extent
              console.log(`Rendering full extent for layer "${layer.name}" (no AOI restrictions)`);
            }

            if (urlParams.toString()) {
              console.log(`Final urlParams before adding to layerUrl:`, urlParams.toString());
              layerUrl += `?${urlParams.toString()}`;
              console.log(`Final layerUrl after adding params:`, layerUrl);
            }



            // Smart bounds handling: AOI bounds vs full layer bounds
            let effectiveBounds = bounds;
            let boundsDescription = 'layer default bounds';

            if (hasValidAOI && aoiData?.bounds && bounds) {
              // Use AOI bounds for clipping when AOI is active
              effectiveBounds = [
                [aoiData.bounds.south, aoiData.bounds.west],
                [aoiData.bounds.north, aoiData.bounds.east]
              ] as [[number, number], [number, number]];
              boundsDescription = 'AOI clipped bounds';

              // Validate bounds
              const boundsValid = effectiveBounds.every(coord => coord.every(val => !isNaN(val) && isFinite(val)));
              console.log(`Layer "${layer.name}" clipped to AOI bounds:`, {
                effectiveBounds,
                originalBounds: bounds,
                aoiBounds: aoiData.bounds,
                boundsValid,
                boundsDescription
              });

              if (!boundsValid) {
                console.error(`❌ Invalid bounds detected for layer "${layer.name}" - this may cause "out of bounds" errors`);
              }
            } else {
              // Use full layer bounds when no AOI or for full-extent viewing
              boundsDescription = hasValidAOI ? 'layer bounds (AOI has no bounds)' : 'full layer bounds (no AOI)';
              console.log(`Layer "${layer.name}" using ${boundsDescription}:`, effectiveBounds);
            }

            // Build AOI signature to force tile refresh on AOI change
            const aoiSignature = hasValidAOI
              ? (aoiData.feature?.id as any) || (aoiData.bounds ? `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}` : 'aoi-no-bounds')
              : 'full-extent';

            // Log the complete WMS URL for debugging
            if (hasValidAOI) {
              console.log(`Complete WMS URL for layer "${layer.name}":`, layerUrl);

              if (urlParams.has('CQL_FILTER')) {
                const cqlFilter = urlParams.get('CQL_FILTER');
                console.log(`CQL_FILTER being applied:`, cqlFilter?.substring(0, 200) + '...');
              } else if (urlParams.has('BBOX')) {
                console.log(`BBOX clipping being applied:`, urlParams.get('BBOX'));
              } else {
                console.log(`⚠️ No clipping parameters found in URL - this may be the issue!`);
              }
            }

            return (

              <WMSTileLayer
                key={`${layer.name}-${aoiSignature}`} // Include AOI signature to force re-render when AOI changes
                url={layerUrl}
                layers={layerName}
                format={format}
                transparent={transparent}
                version="1.1.1"
                bounds={effectiveBounds}
                attribution={`${layer.title}${isRemoteLayer ? ' (Remote)' : ''}${hasValidAOI ? ' (AOI Clipped)' : ' (Full Extent)'}`}
                opacity={layerOpacities[layer.name] ?? 1.0}
                maxZoom={19}
                minZoom={2}
                tileSize={256}
                eventHandlers={{
                  loading: () => {
                    console.log(`Layer "${layer.name}" loading... (${isRemoteLayer ? 'Remote' : 'Local'}${hasValidAOI ? ' with AOI clipping' : ' full extent'})`);
                    handleLayerLoading(layer.name);
                  },
                  load: () => {
                    console.log(` Layer "${layer.name}" loaded successfully (${isRemoteLayer ? 'Remote' : 'Local'}${aoiData ? ' with AOI clipping' : ''})`);
                    if (hasValidAOI) {
                      console.log(`💡 If layer appears empty, it may have no data in the selected AOI`);
                    }
                    handleLayerLoad(layer.name);
                  },
                  error: async (e) => {
                    console.error(`🧩 Layer "${layer.name}" tile error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);

                    // Try to get more details about the error
                    if (e.sourceTarget && e.sourceTarget._url) {
                      const failedUrl = e.sourceTarget._url;
                      console.error(`❌ Failed URL: ${failedUrl}`);

                      // Try to fetch the URL directly to see the server response
                      try {
                        const response = await fetch(failedUrl);
                        if (!response.ok) {
                          const errorText = await response.text();
                          console.error(`Server response (${response.status}):`, errorText);

                          // Check if it's a CQL filter error
                          if (errorText.includes('CQL') || errorText.includes('INTERSECTS') || errorText.includes('geometry')) {
                            console.error(`CQL Filter Error detected - this suggests geometry field name or CQL syntax issue`);
                          }
                        }
                      } catch (fetchError) {
                        console.error(`❌ Could not fetch error details:`, fetchError);
                      }
                    }

                    handleLayerError(layer.name, e);
                  },
                  tileerror: (e) => {
                    console.error(`🧩 Layer "${layer.name}" tile error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);
                    handleLayerError(layer.name, e);
                  },
                  tileloadstart: () => {
                    handleTileLoadStart(layer.name);
                  },
                  tileload: () => {
                    handleTileLoad(layer.name);
                  }
                }}
              />
            );
          })}
        </LayerGroup>

        {/* AOI Clipping Mask - Blue theme with 70% opacity for raster layers */}
        <AOIClipMask
          geometry={aoiData?.geometry && (aoiData.geometry.type === 'Polygon' || aoiData.geometry.type === 'MultiPolygon')
            ? aoiData.geometry as GeoJSON.Polygon | GeoJSON.MultiPolygon
            : null}
          maskFill="#007bff"
          maskOpacity={0.7}
          outlineColor="#0056b3"
          enabled={isRasterActive && Boolean(aoiData?.geometry)}
        />

        {/* Boundary Highlighting Layer for Interactive Filtering */}
        <BoundaryHighlightLayer
          features={highlightedBoundaries}
          onFeatureClick={(feature) => {
            console.log('Boundary feature clicked:', feature.properties);
          }}
        />

        <BoundsCapture onBoundsChange={setMapBounds} />
        <AOIMapController
          aoiData={aoiData}
          selectedLayerNames={selectedLayerNames}
          enableAutoZoom={true}
        />
        <MapResizeHandler sidebarCollapsed={sidebarCollapsed} />
        <MapClickHandler
          wmsLayers={wmsLayers || []} // Provide empty array fallback to prevent initialization errors
          onFeatureInfoClick={handleFeatureInfoClick}
          isDrawingMode={isDrawingMode}
          isCoordinatePinMode={isCoordinatePinMode}
          onCoordinateSelected={onCoordinateSelected}
          setPinCoordinates={setPinCoordinates}
          onPinPlaced={handlePinPlaced}
        />
        <DrawingController
          isDrawingMode={isDrawingMode}
          onCreated={handleCreated}
        />
        {floodRiskAreas.map((area, i) => (
          <Polygon key={`flood-${i}`} positions={area.coordinates} pathOptions={{ color: getRiskColor(area.risk), fillOpacity: 0.4 }} />
        ))}
        {dwsVillages.map((village, i) => (
          <Marker
            key={`village-${i}`}
            position={[village.lat, village.lng]}
            eventHandlers={{ click: () => handleMarkerClick(`village-${i}`) }}
          >
            {activePopup === `village-${i}` && (
              <Popup>
                <h3>{village.name}</h3>
                <p>Population: {village.population}</p>
              </Popup>
            )}
          </Marker>
        ))}

        {/* Coordinate Pin Marker */}
        {isCoordinatePinMode && pinCoordinates && (
          <Marker
            key="coordinate-pin"
            position={[pinCoordinates.lat, pinCoordinates.lng]}
            icon={L.divIcon({
              className: 'coordinate-pin-marker',
              html: `<div style="background-color: #ff5722; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
              iconSize: [16, 16],
              iconAnchor: [8, 8]
            })}
          >
            <Popup>
              <div>
                <strong>Selected Coordinates</strong><br />
                Latitude: {pinCoordinates.lat.toFixed(6)}<br />
                Longitude: {pinCoordinates.lng.toFixed(6)}
              </div>
            </Popup>
          </Marker>
        )}

        {/* New Leaflet Legend Control - replaces old legend system */}
        <LeafletLegend
          visibleLayers={wmsLayers.filter(isLayerVisible)}
          position="bottomleft"
          maxVisibleItems={4}
          selectedLegendLayer={selectedLegendLayer}
          onLegendLayerSelect={handleLegendLayerSelect}
        />
        

  </MapContainer>

      <LoadingOverlay
        loadingLayers={loadingLayers}
        wmsLayers={wmsLayers}
        errorLayers={errorLayers}
        layerProgress={layerProgress}
        onRetryLayer={() => { }} // retry implementation can be added as needed
        onCloseLoader={handleCloseLoader}
      />



      {/* Feature Info Popup */}
      {featureInfo && (
        <FeatureInfoPopup
          featureInfo={featureInfo}
          onClose={() => setFeatureInfo(null)}
          position={popupPosition}
        />
      )}

      {/* AOI Temporal Selection Modal */}
      <TemporalSelectionModal
        show={showTemporalModal}
        onHide={() => setShowTemporalModal(false)}
        onConfirm={handleTemporalConfirm}
        aoiCoordinates={drawnPolygon}
        preSelectedDateRange={dateRange}
      />

      {/* Pin Area Selection Modal */}
      {pinCoordinates && (
        <PinAreaSelectionModal
          show={showPinAreaModal}
          onHide={() => setShowPinAreaModal(false)}
          pinCoordinates={pinCoordinates}
          onAreaSelected={handlePinAreaSelected}
        />
      )}

    </div>
  );
};

export default MapComponent;
