import React from 'react';
import { GeoJSON } from 'react-leaflet';
import L from 'leaflet';

interface BoundaryHighlightLayerProps {
  features: GeoJSON.Feature[];
  onFeatureClick?: (feature: GeoJSON.Feature) => void;
}

const BoundaryHighlightLayer: React.FC<BoundaryHighlightLayerProps> = ({
  features,
  onFeatureClick
}) => {
  // Don't render if no features
  if (!features || features.length === 0) {
    return null;
  }

  // Create GeoJSON collection from features
  const geoJsonData: GeoJSON.FeatureCollection = {
    type: 'FeatureCollection',
    features: features
  };

  const onEachFeature = (feature: GeoJSON.Feature, layer: L.Layer) => {
    // Add click handler
    if (onFeatureClick) {
      layer.on('click', () => {
        onFeatureClick(feature);
      });
    }

    // Add popup with boundary information
    const props = feature.properties || {};
    const popupContent = `
      <div style="font-size: 12px;">
        <strong>Administrative Boundary</strong><br/>
        ${props.adm1_en ? `<strong>Province:</strong> ${props.adm1_en}<br/>` : ''}
        ${props.adm2_en ? `<strong>District:</strong> ${props.adm2_en}<br/>` : ''}
        ${props.adm3_en ? `<strong>Municipality:</strong> ${props.adm3_en}<br/>` : ''}
        ${props.adm4_en ? `<strong>Ward:</strong> ${props.adm4_en}<br/>` : ''}
      </div>
    `;
    
    layer.bindPopup(popupContent);

    // Add hover effects - red outline on hover, no fill overlay
    layer.on('mouseover', function(this: L.Path) {
      this.setStyle({
        fillColor: 'transparent', // No fill on hover
        fillOpacity: 0, // Completely transparent fill
        color: '#dc3545', // Red outline on hover
        weight: 3,
        opacity: 1
      });
    });

    layer.on('mouseout', function(this: L.Path) {
      this.setStyle({
        fillColor: 'transparent', // No fill by default
        fillOpacity: 0, // Completely transparent fill
        color: '#3388ff', // Blue outline by default
        weight: 2,
        opacity: 0.8 // Slightly transparent outline
      });
    });
  };

  const pathOptions = {
    fillColor: 'transparent', // No fill overlay to prevent covering data layers
    fillOpacity: 0, // Completely transparent fill
    color: '#3388ff', // Blue outline
    weight: 2,
    opacity: 0.8, // Slightly transparent outline
    dashArray: '5, 5',
    // Ensure boundaries don't cover data layers
    pane: 'overlayPane' // Use overlay pane with lower z-index than data layers
  };

  return (
    <GeoJSON
      key={`boundary-highlight-${features.length}`}
      data={geoJsonData}
      pathOptions={pathOptions}
      onEachFeature={onEachFeature}
      // Use a specific pane to control z-index layering
      pane="overlayPane"
    />
  );
};

export default BoundaryHighlightLayer;
