import React from 'react';
import './MapLegend.css';

const MapLegend: React.FC = () => {
  const legendItems = [
    { color: 'red', label: 'High' },
    { color: 'yellow', label: 'Moderate' },
    { color: 'green', label: 'Low' },
    { color: 'lightblue', label: 'Very Low' },
    { color: 'blue', label: 'River' },
  ];

  return (
    <div className="map-legend">
      <h3 className="legend-title">Flood Risk</h3>
      <div className="legend-items">
        {legendItems.map((item, index) => (
          <div key={index} className="legend-item">
            <div
              className="legend-color"
              style={{ backgroundColor: item.color }}
            ></div>
            <div className="legend-label">{item.label}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MapLegend;