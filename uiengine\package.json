{"name": "flood-monitoring-uiengine", "version": "1.0.0", "description": "UI Engine service for flood monitoring system", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only --debug src/server.ts", "build": "tsc", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/archiver": "^6.0.3", "@types/multer": "^2.0.0", "archiver": "^7.0.1", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "cron": "^3.1.0", "dotenv": "^16.4.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "jwt-decode": "^3.1.2", "knex": "^3.1.0", "memory-cache": "^0.2.0", "multer": "^2.0.2", "nodemailer": "^6.9.0", "pg": "^8.11.3", "socket.io": "^4.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.10.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.0", "@types/memory-cache": "^0.2.5", "@types/node": "^20.11.16", "@types/nodemailer": "^6.4.0", "@types/pg": "^8.10.9", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/xml2js": "^0.4.14", "jest": "^29.7.0", "socket.io-client": "^4.7.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}