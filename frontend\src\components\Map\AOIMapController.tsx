import { useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import { LatLngBounds } from 'leaflet';

interface AOIMapControllerProps {
  aoiData?: {
    type: 'administrative' | 'drawn' | 'pin' | 'interactive-boundaries';
    bounds?: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    geometry?: any;
    name?: string;
    level?: 'province' | 'district' | 'municipality' | 'ward';
    // Additional properties for interactive boundaries
    features?: GeoJSON.Feature[];
    count?: number;
    area?: number;
  };
  selectedLayerNames: string[];
  enableAutoZoom?: boolean;
}

const AOIMapController: React.FC<AOIMapControllerProps> = ({
  aoiData,
  selectedLayerNames,
  enableAutoZoom = true
}) => {
  const map = useMap();
  const lastAOIRef = useRef<string>('');
  const isInitialLoad = useRef(true);
  const zoomTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!map || !enableAutoZoom) return;

    // Handle reset case - when aoiData is null, reset to default South Africa view
    if (!aoiData) {
      console.log('🗺️ AOI data is null, resetting to default South Africa view');

      // Clear the last AOI reference to allow future updates
      lastAOIRef.current = '';

      // Reset to default South Africa view
      map.setMinZoom(5);
      map.setMaxZoom(18);

      setTimeout(() => {
        map.setView([-29.0, 24.0], 6);
        console.log('🗺️ Map reset to default South Africa position');
      }, 100);

      return;
    }

    // Only proceed if we have valid AOI data with bounds
    if (!aoiData.bounds) {
      console.log('🗺️ No valid AOI bounds, skipping zoom update');
      return;
    }

    // Create a unique identifier for the current AOI
    const currentAOIId = `${aoiData.type}-${aoiData.level || 'unknown'}-${aoiData.name || 'unnamed'}-${JSON.stringify(aoiData.bounds)}`;

    // Skip if this is the same AOI as before
    if (lastAOIRef.current === currentAOIId) {
      console.log('🗺️ Same AOI as before, skipping zoom update');
      return;
    }

    // Clear any existing zoom timeout to debounce rapid changes
    if (zoomTimeoutRef.current) {
      console.log('🔄 Clearing previous zoom timeout (debouncing)');
      clearTimeout(zoomTimeoutRef.current);
    }

    console.log('🗺️ AOI changed, scheduling smooth zoom update:', {
      previousAOI: lastAOIRef.current,
      currentAOI: currentAOIId,
      aoiData: aoiData,
      timestamp: new Date().toISOString()
    });

    // Debounce zoom changes to prevent rapid updates
    zoomTimeoutRef.current = setTimeout(() => {
      console.log('🎯 Executing debounced zoom update');
      performSmoothZoom(map, aoiData, currentAOIId);
    }, 300); // 300ms debounce

  }, [map, aoiData, enableAutoZoom]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (zoomTimeoutRef.current) {
        clearTimeout(zoomTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Perform the actual smooth zoom operation
   */
  const performSmoothZoom = (map: L.Map, aoiData: any, aoiId: string) => {
    // Detailed bounds debugging
    if (aoiData?.bounds) {
      console.log('🔍 Detailed bounds analysis:', {
        rawBounds: aoiData.bounds,
        north: aoiData.bounds.north,
        south: aoiData.bounds.south,
        east: aoiData.bounds.east,
        west: aoiData.bounds.west,
        isValidSouthAfrica: (
          aoiData.bounds.south >= -35 && aoiData.bounds.south <= -22 &&
          aoiData.bounds.north >= -35 && aoiData.bounds.north <= -22 &&
          aoiData.bounds.west >= 16 && aoiData.bounds.west <= 33 &&
          aoiData.bounds.east >= 16 && aoiData.bounds.east <= 33
        )
      });
    }

    lastAOIRef.current = aoiId;

    if (aoiData?.bounds) {
      // Validate bounds are within South Africa
      const bounds = aoiData.bounds;
      const isValidSouthAfricaBounds = (
        bounds.south >= -35 && bounds.south <= -22 &&
        bounds.north >= -35 && bounds.north <= -22 &&
        bounds.west >= 16 && bounds.west <= 33 &&
        bounds.east >= 16 && bounds.east <= 33 &&
        bounds.south < bounds.north &&
        bounds.west < bounds.east
      );

      if (!isValidSouthAfricaBounds) {
        console.error('❌ Invalid bounds detected - not within South Africa:', bounds);
        console.log('🗺️ Falling back to default South Africa view');

        map.setMinZoom(5);
        map.setMaxZoom(18);
        setTimeout(() => {
          map.setView([-29.0, 24.0], 6);
        }, 100);
        return;
      }

      // Calculate bounds size for zoom level determination
      const boundsSize = Math.max(
        bounds.north - bounds.south,
        bounds.east - bounds.west
      );

      // Calculate appropriate zoom level based on AOI size and level
      let targetZoom = 6; // Default South Africa view
      let minZoom = 5;
      let maxZoom = 18;

      if (aoiData.level === 'province') {
        // Provinces are large - use moderate zoom
        targetZoom = boundsSize > 10 ? 6 : boundsSize > 6 ? 7 : 8;
        minZoom = 5;
        console.log(`🗺️ Province zoom: size=${boundsSize.toFixed(2)}, zoom=${targetZoom}`);
      } else if (aoiData.level === 'district') {
        // Districts/metros are medium-sized
        targetZoom = boundsSize > 4 ? 7 : boundsSize > 2 ? 8 : boundsSize > 1 ? 9 : 10;
        minZoom = 6;
        console.log(`🗺️ District zoom: size=${boundsSize.toFixed(2)}, zoom=${targetZoom}`);
      } else if (aoiData.level === 'municipality') {
        // Municipalities are smaller
        targetZoom = boundsSize > 2 ? 8 : boundsSize > 1 ? 9 : boundsSize > 0.5 ? 10 : 11;
        minZoom = 7;
        console.log(`🗺️ Municipality zoom: size=${boundsSize.toFixed(2)}, zoom=${targetZoom}`);
      } else if (aoiData.level === 'ward') {
        // Wards are very small
        targetZoom = boundsSize > 0.5 ? 10 : boundsSize > 0.2 ? 11 : 12;
        minZoom = 9;
        console.log(`🗺️ Ward zoom: size=${boundsSize.toFixed(2)}, zoom=${targetZoom}`);
      } else {
        // For drawn or pin AOIs, calculate based on size only
        targetZoom = boundsSize > 5 ? 7 : boundsSize > 1 ? 9 : boundsSize > 0.5 ? 11 : 13;
        minZoom = 6;
        console.log(`🗺️ Custom AOI zoom: size=${boundsSize.toFixed(2)}, zoom=${targetZoom}`);
      }

      console.log('🎯 Calculated zoom parameters:', {
        aoiLevel: aoiData.level,
        boundsSize: boundsSize.toFixed(3),
        targetZoom,
        minZoom,
        maxZoom,
        bounds: bounds
      });

      // Create Leaflet bounds using validated bounds
      const leafletBounds = new LatLngBounds(
        [bounds.south, bounds.west],
        [bounds.north, bounds.east]
      );

      console.log('🗺️ Creating Leaflet bounds:', {
        southWest: [bounds.south, bounds.west],
        northEast: [bounds.north, bounds.east],
        center: [(bounds.north + bounds.south) / 2, (bounds.east + bounds.west) / 2]
      });

      // Apply zoom restrictions
      map.setMinZoom(minZoom);
      map.setMaxZoom(maxZoom);

      // Get current zoom to determine if we need smooth transition
      const currentZoom = map.getZoom();
      const zoomDifference = Math.abs(targetZoom - currentZoom);

      console.log('🎯 Zoom transition analysis:', {
        currentZoom: currentZoom.toFixed(2),
        targetZoom,
        zoomDifference: zoomDifference.toFixed(2),
        needsSmoothTransition: zoomDifference > 3
      });

      // Use smooth zoom for large zoom changes to prevent freezing
      if (zoomDifference > 3) {
        console.log('🔄 Using smooth progressive zoom for large change');
        smoothZoomToBounds(map, leafletBounds, targetZoom, currentZoom);
      } else {
        console.log('🎯 Using direct zoom for small change');
        directZoomToBounds(map, leafletBounds, targetZoom);
      }

      isInitialLoad.current = false;

    } else {
      // No AOI data - reset to default South Africa view
      console.log('🗺️ No AOI data, resetting to default South Africa view');
      
      map.setMinZoom(5);
      map.setMaxZoom(18);
      
      setTimeout(() => {
        map.setView([-29.0, 24.0], 6);
      }, 100);
    }
  }; // End of performSmoothZoom function

  // Respond to layer changes - but only when layers are first added
  useEffect(() => {
    if (!map || !aoiData?.bounds || selectedLayerNames.length === 0) return;

    // Only re-focus if we have layers and this seems like an initial load
    // Avoid constant re-focusing when layers change
    const isInitialLayerLoad = selectedLayerNames.length > 0;

    if (isInitialLayerLoad) {
      console.log('🗂️ Initial layers loaded, gentle AOI focus check');

      // Very gentle check - only re-focus if map is way off
      setTimeout(() => {
        const currentCenter = map.getCenter();
        const aoiCenter = {
          lat: (aoiData.bounds.north + aoiData.bounds.south) / 2,
          lng: (aoiData.bounds.east + aoiData.bounds.west) / 2
        };

        // Only re-focus if center is very far from AOI center (>2 degrees)
        const distance = Math.abs(currentCenter.lat - aoiCenter.lat) + Math.abs(currentCenter.lng - aoiCenter.lng);
        if (distance > 2) {
          console.log('🗂️ Map drifted far from AOI, gentle re-focus');
          map.setView([aoiCenter.lat, aoiCenter.lng], map.getZoom());
        }
      }, 2000); // Longer delay to avoid conflicts
    }

  }, [map, selectedLayerNames.length]); // Removed aoiData?.bounds to prevent loops

  /**
   * Smooth progressive zoom for large zoom changes to prevent freezing
   */
  const smoothZoomToBounds = (map: L.Map, bounds: L.LatLngBounds, targetZoom: number, currentZoom: number) => {
    console.log('🔄 Starting smooth zoom transition...');

    // Calculate zoom steps (max 2 zoom levels per step)
    const zoomDifference = targetZoom - currentZoom;
    const zoomDirection = zoomDifference > 0 ? 1 : -1;
    const maxStepSize = 2;
    const steps = Math.ceil(Math.abs(zoomDifference) / maxStepSize);

    console.log('🔄 Smooth zoom plan:', {
      currentZoom: currentZoom.toFixed(2),
      targetZoom,
      zoomDifference: zoomDifference.toFixed(2),
      steps,
      direction: zoomDirection > 0 ? 'zoom in' : 'zoom out'
    });

    let currentStep = 0;

    const executeZoomStep = () => {
      if (currentStep >= steps) {
        // Final step: fit to exact bounds
        console.log('🎯 Final zoom step: fitting to exact bounds');
        directZoomToBounds(map, bounds, targetZoom);
        return;
      }

      // Calculate intermediate zoom level
      const remainingSteps = steps - currentStep;
      const remainingZoom = targetZoom - map.getZoom();
      const stepZoom = Math.min(Math.abs(remainingZoom), maxStepSize) * zoomDirection;
      const intermediateZoom = Math.round(map.getZoom() + stepZoom);

      console.log(`🔄 Zoom step ${currentStep + 1}/${steps}: ${map.getZoom().toFixed(2)} → ${intermediateZoom}`);

      // Apply intermediate zoom with animation
      map.setView(bounds.getCenter(), intermediateZoom, {
        animate: true,
        duration: 0.3, // 300ms animation
        easeLinearity: 0.25
      });

      currentStep++;

      // Schedule next step
      setTimeout(executeZoomStep, 400); // Wait for animation + buffer
    };

    // Start the progressive zoom
    executeZoomStep();
  };

  /**
   * Direct zoom for small changes
   */
  const directZoomToBounds = (map: L.Map, bounds: L.LatLngBounds, targetZoom: number) => {
    const fitOptions = {
      padding: [20, 20] as [number, number],
      maxZoom: targetZoom,
      animate: true,
      duration: 0.5, // 500ms animation
      easeLinearity: 0.25
    };

    console.log('🎯 Direct zoom to bounds with options:', fitOptions);

    // Use a slight delay to ensure map is ready
    setTimeout(() => {
      try {
        map.fitBounds(bounds, fitOptions);

        // Verify the zoom was applied
        setTimeout(() => {
          const currentZoom = map.getZoom();
          const currentCenter = map.getCenter();
          console.log('✅ Map zoom applied:', {
            targetZoom,
            actualZoom: currentZoom.toFixed(2),
            center: [currentCenter.lat.toFixed(4), currentCenter.lng.toFixed(4)]
          });
        }, 600); // Wait for animation to complete

      } catch (error) {
        console.error('❌ Error fitting map to bounds:', error);
      }
    }, 100);
  };

  return null; // This component doesn't render anything
};

export default AOIMapController;
