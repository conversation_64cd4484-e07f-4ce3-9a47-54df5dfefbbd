import { useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import LeafletLegendControl from './LeafletLegendControl';
import { API_CONFIG } from '../../config';
import { WMSLayer } from '../../services/geoserverService';

interface LeafletLegendProps {
  visibleLayers?: WMSLayer[];
  position?: string;
  maxVisibleItems?: number;
  selectedLegendLayer?: string;
  onLegendLayerSelect?: (layerName: string) => void;
}

const LeafletLegend: React.FC<LeafletLegendProps> = ({
  visibleLayers = [],
  position = 'bottomleft',
  maxVisibleItems = 4,
  selectedLegendLayer,
  onLegendLayerSelect
}) => {
  const map = useMap();
  const legendControlRef = useRef(null);

  useEffect(() => {
    // Clean up any existing control first (handles React StrictMode double-mounting)
    if (legendControlRef.current) {
      if (map.hasLayer(legendControlRef.current)) {
        map.removeControl(legendControlRef.current);
      }
      legendControlRef.current = null;
    }

    // Create the legend control
    const legendControl = new LeafletLegendControl({
      position: position,
      maxVisibleItems: maxVisibleItems
    });



    // Add to map
    legendControl.addTo(map);
    legendControlRef.current = legendControl;

    // Add class to map container to handle attribution positioning
    const mapContainer = map.getContainer();
    if (mapContainer) {
      mapContainer.classList.add('legend-active');
    }

    // Cleanup function
    return () => {
      if (legendControlRef.current && map.hasLayer(legendControlRef.current)) {
        map.removeControl(legendControlRef.current);
      }
      legendControlRef.current = null;
      // Remove class from map container
      const mapContainer = map.getContainer();
      if (mapContainer) {
        mapContainer.classList.remove('legend-active');
      }
    };
  }, [map, position, maxVisibleItems]);

  // Update legend data when layers change
  useEffect(() => {
    if (legendControlRef.current && visibleLayers) {
      const legendData = visibleLayers.map(layer => {
        // Determine if layer is temporal based on metadata
        const isTemporalLayer = layer.temporal || 
          (layer.name && (
            layer.name.includes('flood') || 
            layer.name.includes('sentinel') || 
            layer.name.includes('modis') ||
            layer.name.includes('temporal')
          ));

        // Determine if layer is queryable
        const isQueryableLayer = layer.queryable || 
          (layer.name && (
            layer.name.includes('village') ||
            layer.name.includes('cadastre') ||
            layer.name.includes('admin')
          ));

        return {
          name: layer.name,
          title: layer.title || layer.name,
          legendUrl: `${API_CONFIG.BASE_URL}/ows/wms-proxy?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${layer.name}&FORMAT=image/png&VERSION=1.1.1`,
          temporal: isTemporalLayer,
          queryable: isQueryableLayer,
          selected: selectedLegendLayer === layer.name
        };
      });

      legendControlRef.current.updateLegends(legendData);
    }
  }, [visibleLayers, selectedLegendLayer]);

  return null; // This component doesn't render anything directly
};

export default LeafletLegend;
