import { Request, Response, NextFunction } from 'express';
import mcache from 'memory-cache';

export const cacheMiddleware = (duration: number) => {
    return (req: Request, res: Response, next: NextFunction) => {
        const key = `__express__${req.originalUrl || req.url}`;
        const cachedBody = mcache.get(key);

        if (cachedBody) {
            res.send(cachedBody);
            return;
        }

        const originalSend = res.send.bind(res);
        res.send = ((body: any) => {
            mcache.put(key, body, duration * 1000);
            return originalSend(body);
        }) as any;

        next();
    };
};
