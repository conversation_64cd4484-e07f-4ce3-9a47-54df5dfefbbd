.map-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.map {
  height: 100%;
  width: 100%;
}

/* Drawing mode cursor */
.map.drawing-mode {
  cursor: crosshair !important;
}

.map.drawing-mode * {
  cursor: crosshair !important;
}

/* Pin mode cursor */
.map.pin-mode {
  cursor: crosshair !important;
}

.map.pin-mode * {
  cursor: crosshair !important;
}

/* Override leaflet draw styles */
.leaflet-draw-toolbar a {
  background-color: #fff;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.leaflet-draw-toolbar a:hover {
  background-color: #f4f4f4;
}

.leaflet-control-zoom a {
  color: #333;
}

/* Layer status overlays */
.layer-loading-overlay,
.layer-error-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  z-index: 1000;
  font-size: 14px;
}

.layer-loading-overlay {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
}

.layer-error-overlay {
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
}



@media (max-width: 768px) {

  .map-wrapper,
  .map {
    height: 50vh;
  }


}