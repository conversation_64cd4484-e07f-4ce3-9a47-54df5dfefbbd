import axios from 'axios';
import { DatabaseService } from './databaseService';

export interface TimeSeriesQuery {
  geometry: GeoJSON.Polygon;
  startDate: string;
  endDate: string;
  analysisType: 'static_mosaic' | 'ndvi_trend' | 'vci_analysis' | 'anomaly_detection' | 'seasonal_patterns';
  layers: string[];
  aoiId?: number;
}

export interface TimeSeriesResult {
  analysisId: string;
  analysisType: string;
  dateRange: { startDate: string; endDate: string };
  geometry: GeoJSON.Polygon;
  products: {
    staticMosaic?: StaticMosaicProduct;
    ndviTrend?: NDVITrendProduct;
    vciAnalysis?: VCIAnalysisProduct;
    anomalies?: AnomalyProduct;
    seasonalPatterns?: SeasonalProduct;
  };
  metadata: {
    processingTime: number;
    dataPoints: number;
    qualityScore: number;
    generatedAt: string;
  };
}

export interface StaticMosaicProduct {
  type: 'static_mosaic';
  description: 'Single composite image for the selected date range';
  wmsUrl: string;
  downloadUrl?: string;
  coverage: {
    startDate: string;
    endDate: string;
    cloudCover: number;
  };
}

export interface NDVITrendProduct {
  type: 'ndvi_trend';
  description: 'NDVI trend analysis over time';
  trendData: {
    dates: string[];
    values: number[];
    trend: 'increasing' | 'decreasing' | 'stable';
    slope: number;
    correlation: number;
  };
  stackedLayers: string[];
  visualizationUrl: string;
  statistics: {
    mean: number;
    min: number;
    max: number;
    stdDev: number;
  };
}

export interface VCIAnalysisProduct {
  type: 'vci_analysis';
  description: 'Vegetation Condition Index analysis';
  vciData: {
    dates: string[];
    vciValues: number[];
    droughtConditions: Array<{
      date: string;
      severity: 'normal' | 'mild' | 'moderate' | 'severe' | 'extreme';
      vciValue: number;
    }>;
  };
  thresholds: {
    extreme: number;
    severe: number;
    moderate: number;
    mild: number;
  };
}

export interface AnomalyProduct {
  type: 'anomaly_detection';
  description: 'Statistical anomaly detection in temporal data';
  anomalies: Array<{
    date: string;
    value: number;
    expectedValue: number;
    deviation: number;
    severity: 'low' | 'medium' | 'high';
    type: 'positive' | 'negative';
  }>;
  statistics: {
    totalAnomalies: number;
    anomalyRate: number;
    detectionMethod: string;
  };
}

export interface SeasonalProduct {
  type: 'seasonal_patterns';
  description: 'Seasonal pattern analysis';
  patterns: {
    monthlyAverages: Array<{ month: number; value: number; }>;
    seasonalTrends: Array<{
      season: 'spring' | 'summer' | 'autumn' | 'winter';
      trend: 'increasing' | 'decreasing' | 'stable';
      averageValue: number;
    }>;
    cyclicalPatterns: {
      detected: boolean;
      period: number; // in months
      amplitude: number;
    };
  };
}

export class TimeSeriesService {
  private static readonly GEOSERVER_URL = process.env.GEOSERVER_BASE_URL || 'http://localhost:8080/geoserver';
  private static readonly TEMPORAL_LAYERS = ['flood_risk', 'soil_moisture', 'mosaic', 'ndvi', 'sentinel', 'landsat'];

  /**
   * Main entry point for time-series analysis
   */
  static async performTimeSeriesAnalysis(query: TimeSeriesQuery): Promise<TimeSeriesResult> {
    const startTime = Date.now();
    console.log(`🕒 Starting time-series analysis: ${query.analysisType}`);

    try {
      // Validate temporal layers
      const temporalLayers = this.filterTemporalLayers(query.layers);
      if (temporalLayers.length === 0) {
        throw new Error('No temporal layers found in the query');
      }

      // Generate analysis ID
      const analysisId = `ts_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Perform analysis based on type
      let products: any = {};
      
      switch (query.analysisType) {
        case 'static_mosaic':
          products.staticMosaic = await this.generateStaticMosaic(query, temporalLayers);
          break;
        case 'ndvi_trend':
          products.ndviTrend = await this.analyzeNDVITrend(query, temporalLayers);
          break;
        case 'vci_analysis':
          products.vciAnalysis = await this.analyzeVCI(query, temporalLayers);
          break;
        case 'anomaly_detection':
          products.anomalies = await this.detectAnomalies(query, temporalLayers);
          break;
        case 'seasonal_patterns':
          products.seasonalPatterns = await this.analyzeSeasonalPatterns(query, temporalLayers);
          break;
        default:
          throw new Error(`Unsupported analysis type: ${query.analysisType}`);
      }

      const processingTime = Date.now() - startTime;

      const result: TimeSeriesResult = {
        analysisId,
        analysisType: query.analysisType,
        dateRange: { startDate: query.startDate, endDate: query.endDate },
        geometry: query.geometry,
        products,
        metadata: {
          processingTime,
          dataPoints: await this.countDataPoints(query, temporalLayers),
          qualityScore: await this.calculateQualityScore(query, temporalLayers),
          generatedAt: new Date().toISOString()
        }
      };

      // Save analysis to database if available
      await this.saveAnalysisResult(result);

      console.log(`✅ Time-series analysis completed in ${processingTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ Time-series analysis failed:', error);
      throw error;
    }
  }

  /**
   * Filter layers to only include temporal ones
   */
  private static filterTemporalLayers(layers: string[]): string[] {
    return layers.filter(layer => 
      this.TEMPORAL_LAYERS.some(temporal => 
        layer.toLowerCase().includes(temporal)
      )
    );
  }

  /**
   * Generate static mosaic (current behavior)
   */
  private static async generateStaticMosaic(query: TimeSeriesQuery, layers: string[]): Promise<StaticMosaicProduct> {
    console.log('📸 Generating static mosaic...');
    
    // This maintains current functionality but with clear labeling
    const wmsUrl = this.buildWMSUrl(layers[0], query.geometry, query.startDate, query.endDate);
    
    return {
      type: 'static_mosaic',
      description: 'Single composite image for the selected date range',
      wmsUrl,
      coverage: {
        startDate: query.startDate,
        endDate: query.endDate,
        cloudCover: Math.random() * 30 // Placeholder - would be calculated from actual data
      }
    };
  }

  /**
   * Analyze NDVI trends over time
   */
  private static async analyzeNDVITrend(query: TimeSeriesQuery, layers: string[]): Promise<NDVITrendProduct> {
    console.log('📈 Analyzing NDVI trends...');
    
    // Generate time series data points
    const timePoints = this.generateTimePoints(query.startDate, query.endDate, 'monthly');
    const ndviValues = await this.extractNDVIValues(query.geometry, timePoints, layers);
    
    // Calculate trend statistics
    const trend = this.calculateTrend(ndviValues);
    const statistics = this.calculateStatistics(ndviValues);
    
    return {
      type: 'ndvi_trend',
      description: 'NDVI trend analysis over time',
      trendData: {
        dates: timePoints,
        values: ndviValues,
        trend: trend.direction,
        slope: trend.slope,
        correlation: trend.correlation
      },
      stackedLayers: layers,
      visualizationUrl: this.buildVisualizationUrl('ndvi_trend', query),
      statistics
    };
  }

  /**
   * Analyze Vegetation Condition Index
   */
  private static async analyzeVCI(query: TimeSeriesQuery, layers: string[]): Promise<VCIAnalysisProduct> {
    console.log('🌱 Analyzing VCI...');
    
    const timePoints = this.generateTimePoints(query.startDate, query.endDate, 'monthly');
    const ndviValues = await this.extractNDVIValues(query.geometry, timePoints, layers);
    
    // Calculate VCI values (simplified calculation)
    const vciValues = this.calculateVCI(ndviValues);
    const droughtConditions = this.assessDroughtConditions(timePoints, vciValues);
    
    return {
      type: 'vci_analysis',
      description: 'Vegetation Condition Index analysis',
      vciData: {
        dates: timePoints,
        vciValues,
        droughtConditions
      },
      thresholds: {
        extreme: 10,
        severe: 20,
        moderate: 35,
        mild: 50
      }
    };
  }

  /**
   * Detect statistical anomalies
   */
  private static async detectAnomalies(query: TimeSeriesQuery, layers: string[]): Promise<AnomalyProduct> {
    console.log('🔍 Detecting anomalies...');
    
    const timePoints = this.generateTimePoints(query.startDate, query.endDate, 'monthly');
    const values = await this.extractNDVIValues(query.geometry, timePoints, layers);
    
    const anomalies = this.detectStatisticalAnomalies(timePoints, values);
    
    return {
      type: 'anomaly_detection',
      description: 'Statistical anomaly detection in temporal data',
      anomalies,
      statistics: {
        totalAnomalies: anomalies.length,
        anomalyRate: (anomalies.length / values.length) * 100,
        detectionMethod: 'Z-score with 2-sigma threshold'
      }
    };
  }

  /**
   * Analyze seasonal patterns
   */
  private static async analyzeSeasonalPatterns(query: TimeSeriesQuery, layers: string[]): Promise<SeasonalProduct> {
    console.log('🍂 Analyzing seasonal patterns...');
    
    const timePoints = this.generateTimePoints(query.startDate, query.endDate, 'monthly');
    const values = await this.extractNDVIValues(query.geometry, timePoints, layers);
    
    const monthlyAverages = this.calculateMonthlyAverages(timePoints, values);
    const seasonalTrends = this.calculateSeasonalTrends(monthlyAverages);
    const cyclicalPatterns = this.detectCyclicalPatterns(values);
    
    return {
      type: 'seasonal_patterns',
      description: 'Seasonal pattern analysis',
      patterns: {
        monthlyAverages,
        seasonalTrends,
        cyclicalPatterns
      }
    };
  }

  /**
   * Generate time points for analysis
   */
  private static generateTimePoints(startDate: string, endDate: string, interval: 'daily' | 'weekly' | 'monthly'): string[] {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const points: string[] = [];

    let current = new Date(start);

    while (current <= end) {
      points.push(current.toISOString().split('T')[0]);

      switch (interval) {
        case 'daily':
          current.setDate(current.getDate() + 1);
          break;
        case 'weekly':
          current.setDate(current.getDate() + 7);
          break;
        case 'monthly':
          current.setMonth(current.getMonth() + 1);
          break;
      }
    }

    return points;
  }

  /**
   * Extract NDVI values for given geometry and time points
   */
  private static async extractNDVIValues(geometry: GeoJSON.Polygon, timePoints: string[], layers: string[]): Promise<number[]> {
    // Simplified implementation - in production, this would query actual satellite data
    // For now, generate realistic NDVI values with seasonal patterns

    return timePoints.map((date, index) => {
      const month = new Date(date).getMonth();

      // Simulate seasonal NDVI pattern (higher in growing season)
      const seasonalBase = 0.3 + 0.4 * Math.sin((month - 2) * Math.PI / 6);

      // Add some random variation
      const noise = (Math.random() - 0.5) * 0.2;

      // Ensure NDVI stays within valid range [-1, 1]
      return Math.max(-1, Math.min(1, seasonalBase + noise));
    });
  }

  /**
   * Calculate trend statistics
   */
  private static calculateTrend(values: number[]): { direction: 'increasing' | 'decreasing' | 'stable'; slope: number; correlation: number } {
    if (values.length < 2) {
      return { direction: 'stable', slope: 0, correlation: 0 };
    }

    // Simple linear regression
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

    // Calculate correlation coefficient
    const meanX = sumX / n;
    const meanY = sumY / n;
    const numerator = x.reduce((sum, xi, i) => sum + (xi - meanX) * (values[i] - meanY), 0);
    const denomX = Math.sqrt(x.reduce((sum, xi) => sum + (xi - meanX) ** 2, 0));
    const denomY = Math.sqrt(values.reduce((sum, yi) => sum + (yi - meanY) ** 2, 0));
    const correlation = numerator / (denomX * denomY);

    const direction = Math.abs(slope) < 0.001 ? 'stable' : slope > 0 ? 'increasing' : 'decreasing';

    return { direction, slope, correlation };
  }

  /**
   * Calculate basic statistics
   */
  private static calculateStatistics(values: number[]): { mean: number; min: number; max: number; stdDev: number } {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    const variance = values.reduce((sum, val) => sum + (val - mean) ** 2, 0) / values.length;
    const stdDev = Math.sqrt(variance);

    return { mean, min, max, stdDev };
  }

  /**
   * Calculate VCI values from NDVI
   */
  private static calculateVCI(ndviValues: number[]): number[] {
    // VCI = ((NDVI - NDVImin) / (NDVImax - NDVImin)) * 100
    const min = Math.min(...ndviValues);
    const max = Math.max(...ndviValues);
    const range = max - min;

    if (range === 0) return ndviValues.map(() => 50); // Default to normal conditions

    return ndviValues.map(ndvi => ((ndvi - min) / range) * 100);
  }

  /**
   * Assess drought conditions based on VCI
   */
  private static assessDroughtConditions(dates: string[], vciValues: number[]): Array<{
    date: string;
    severity: 'normal' | 'mild' | 'moderate' | 'severe' | 'extreme';
    vciValue: number;
  }> {
    return dates.map((date, i) => {
      const vci = vciValues[i];
      let severity: 'normal' | 'mild' | 'moderate' | 'severe' | 'extreme';

      if (vci >= 50) severity = 'normal';
      else if (vci >= 35) severity = 'mild';
      else if (vci >= 20) severity = 'moderate';
      else if (vci >= 10) severity = 'severe';
      else severity = 'extreme';

      return { date, severity, vciValue: vci };
    });
  }

  /**
   * Detect statistical anomalies using Z-score
   */
  private static detectStatisticalAnomalies(dates: string[], values: number[]): Array<{
    date: string;
    value: number;
    expectedValue: number;
    deviation: number;
    severity: 'low' | 'medium' | 'high';
    type: 'positive' | 'negative';
  }> {
    const stats = this.calculateStatistics(values);
    const threshold = 2; // 2-sigma threshold

    return dates.map((date, i) => {
      const value = values[i];
      const zScore = Math.abs(value - stats.mean) / stats.stdDev;

      if (zScore > threshold) {
        const deviation = value - stats.mean;
        const severity = zScore > 3 ? 'high' : zScore > 2.5 ? 'medium' : 'low';
        const type = deviation > 0 ? 'positive' : 'negative';

        return {
          date,
          value,
          expectedValue: stats.mean,
          deviation,
          severity,
          type
        };
      }
      return null;
    }).filter(Boolean) as any[];
  }

  /**
   * Calculate monthly averages
   */
  private static calculateMonthlyAverages(dates: string[], values: number[]): Array<{ month: number; value: number; }> {
    const monthlyData: { [key: number]: number[] } = {};

    dates.forEach((date, i) => {
      const month = new Date(date).getMonth();
      if (!monthlyData[month]) monthlyData[month] = [];
      monthlyData[month].push(values[i]);
    });

    return Object.entries(monthlyData).map(([month, vals]) => ({
      month: parseInt(month),
      value: vals.reduce((a, b) => a + b, 0) / vals.length
    }));
  }

  /**
   * Calculate seasonal trends
   */
  private static calculateSeasonalTrends(monthlyAverages: Array<{ month: number; value: number; }>): Array<{
    season: 'spring' | 'summer' | 'autumn' | 'winter';
    trend: 'increasing' | 'decreasing' | 'stable';
    averageValue: number;
  }> {
    const seasons = {
      spring: [2, 3, 4], // Mar, Apr, May
      summer: [5, 6, 7], // Jun, Jul, Aug
      autumn: [8, 9, 10], // Sep, Oct, Nov
      winter: [11, 0, 1] // Dec, Jan, Feb
    };

    return Object.entries(seasons).map(([season, months]) => {
      const seasonData = monthlyAverages.filter(m => months.includes(m.month));
      const averageValue = seasonData.reduce((sum, m) => sum + m.value, 0) / seasonData.length;

      // Simple trend calculation (would be more sophisticated in production)
      const trend = 'stable' as 'increasing' | 'decreasing' | 'stable';

      return {
        season: season as 'spring' | 'summer' | 'autumn' | 'winter',
        trend,
        averageValue
      };
    });
  }

  /**
   * Detect cyclical patterns
   */
  private static detectCyclicalPatterns(values: number[]): {
    detected: boolean;
    period: number;
    amplitude: number;
  } {
    // Simplified cyclical pattern detection
    // In production, this would use FFT or autocorrelation

    return {
      detected: values.length >= 12, // Assume annual cycle if we have enough data
      period: 12, // Monthly data, annual cycle
      amplitude: Math.max(...values) - Math.min(...values)
    };
  }

  /**
   * Build WMS URL for static mosaic
   */
  private static buildWMSUrl(layer: string, geometry: GeoJSON.Polygon, startDate: string, endDate: string): string {
    const bounds = this.calculateBounds(geometry);
    const params = new URLSearchParams({
      SERVICE: 'WMS',
      REQUEST: 'GetMap',
      LAYERS: layer,
      FORMAT: 'image/png',
      TRANSPARENT: 'true',
      VERSION: '1.1.1',
      WIDTH: '512',
      HEIGHT: '512',
      SRS: 'EPSG:4326',
      BBOX: `${bounds.west},${bounds.south},${bounds.east},${bounds.north}`,
      TIME: `${startDate}/${endDate}`
    });

    return `${this.GEOSERVER_URL}/wms?${params.toString()}`;
  }

  /**
   * Build visualization URL for time-series products
   */
  private static buildVisualizationUrl(analysisType: string, query: TimeSeriesQuery): string {
    return `/api/time-series/visualization/${analysisType}?` + new URLSearchParams({
      startDate: query.startDate,
      endDate: query.endDate,
      layers: query.layers.join(',')
    }).toString();
  }

  /**
   * Calculate bounding box from geometry
   */
  private static calculateBounds(geometry: GeoJSON.Polygon): { west: number; south: number; east: number; north: number } {
    const coordinates = geometry.coordinates[0];
    const lngs = coordinates.map(coord => coord[0]);
    const lats = coordinates.map(coord => coord[1]);

    return {
      west: Math.min(...lngs),
      south: Math.min(...lats),
      east: Math.max(...lngs),
      north: Math.max(...lats)
    };
  }

  /**
   * Count data points for quality assessment
   */
  private static async countDataPoints(query: TimeSeriesQuery, layers: string[]): Promise<number> {
    // Simplified - would query actual data availability
    const timePoints = this.generateTimePoints(query.startDate, query.endDate, 'monthly');
    return timePoints.length * layers.length;
  }

  /**
   * Calculate quality score based on data availability and coverage
   */
  private static async calculateQualityScore(query: TimeSeriesQuery, layers: string[]): Promise<number> {
    // Simplified quality score calculation
    const timePoints = this.generateTimePoints(query.startDate, query.endDate, 'monthly');
    const dataPoints = timePoints.length;

    // Quality factors
    const temporalCoverage = Math.min(dataPoints / 12, 1); // Prefer at least 12 months
    const layerCoverage = Math.min(layers.length / 3, 1); // Prefer multiple layers
    const cloudCoverage = 0.8; // Assume 80% cloud-free data

    return Math.round((temporalCoverage * layerCoverage * cloudCoverage) * 100);
  }

  /**
   * Save analysis result to database
   */
  private static async saveAnalysisResult(result: TimeSeriesResult): Promise<void> {
    try {
      const dbAvailable = await DatabaseService.testConnection();
      if (!dbAvailable) {
        console.log('📝 Database unavailable, skipping analysis save');
        return;
      }

      // Save to spatial_analyses table with temporal_change type
      const db = DatabaseService.getDatabase();
      await db('spatial_analyses').insert({
        analysis_type: 'temporal_change',
        start_date: result.dateRange.startDate,
        end_date: result.dateRange.endDate,
        results: JSON.stringify(result),
        geometry: JSON.stringify(result.geometry),
        processing_time_ms: result.metadata.processingTime,
        analysis_date: new Date(),
        metadata: JSON.stringify({
          analysisId: result.analysisId,
          analysisType: result.analysisType,
          qualityScore: result.metadata.qualityScore,
          dataPoints: result.metadata.dataPoints
        })
      });

      console.log(`💾 Time-series analysis saved: ${result.analysisId}`);
    } catch (error) {
      console.warn('⚠️ Failed to save analysis result:', error);
    }
  }
}
