/* Loading Overlay Styles - Non-blocking notification */
.loading-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2000;
  /* Removed background overlay to prevent blocking map interaction */
  pointer-events: none; /* Allow map interaction underneath */
}

.loading-content {
  background: #fff;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  max-width: 350px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  pointer-events: auto; /* Allow interaction with the loader itself */
}

.loading-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: -20px -20px 1.5rem -20px;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
}

/* Blue header styling for loading overlay */
.loading-header.app-header-blue {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: #ffffff !important;
  border-bottom: 1px solid #004085 !important;
}

.loading-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: #0a4273;
  font-weight: 600;
  flex: 1;
}

.loading-header.app-header-blue h4 {
  color: #ffffff !important;
}

.loading-header.app-header-blue .spinner-border {
  color: #ffffff !important;
}

.close-overlay-btn {
  padding: 0.25rem !important;
  color: #6b7280 !important;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  margin-left: auto;
}

.close-overlay-btn:hover {
  color: #374151 !important;
  background: rgba(107, 114, 128, 0.1) !important;
  border-radius: 4px;
}

.loading-layers {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loading-layer-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.loading-layer-item.error {
  border-left: 4px solid #dc3545;
  padding-left: 0.75rem;
  background: rgba(220, 53, 69, 0.05);
  border-radius: 4px;
}

.loading-layer-item.success {
  border-left: 4px solid #10b981;
  padding-left: 0.75rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 4px;
}

.layer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.layer-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dismiss-error-btn {
  padding: 0.25rem !important;
  color: #dc3545 !important;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
}

.dismiss-error-btn:hover {
  color: #b02a37 !important;
  background: rgba(220, 53, 69, 0.1) !important;
  border-radius: 4px;
}

.dismiss-layer-btn {
  padding: 0.25rem !important;
  color: #6b7280 !important;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
}

.dismiss-layer-btn:hover {
  color: #374151 !important;
  background: rgba(107, 114, 128, 0.1) !important;
  border-radius: 4px;
}

.layer-name {
  font-weight: 500;
  color: #495057;
  font-size: 0.95rem;
  flex: 1;
}

.layer-percentage {
  font-weight: 600;
  color: #0a4273;
  font-size: 0.9rem;
  min-width: 40px;
  text-align: right;
}

.error-icon {
  margin-right: 0.5rem;
}

.error-message {
  margin-top: 0.25rem;
  padding: 0.5rem;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 4px;
  border-left: 3px solid #dc3545;
}

.error-message small {
  color: #dc3545;
  font-weight: 500;
}

.retry-btn {
  font-size: 0.75rem !important;
  padding: 0.25rem 0.5rem !important;
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
  display: flex;
  align-items: center;
}

.retry-btn:hover:not(:disabled) {
  background-color: #3b82f6 !important;
  color: white !important;
}

.retry-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dismiss-btn {
  font-size: 0.75rem !important;
  padding: 0.25rem 0.5rem !important;
  border-color: #6b7280 !important;
  color: #6b7280 !important;
}

.dismiss-btn:hover {
  background-color: #6b7280 !important;
  color: white !important;
}

.layer-progress {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.layer-progress .progress-bar {
  transition: width 0.3s ease;
}

.overall-progress {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

/* Animation for progress bars */
.layer-progress .progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
  100% {
    background-position-x: 0;
  }
}

/* Loading spinner animation */
.loading-header .spinner-border {
  color: #0a4273;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .loading-content {
    margin: 1rem;
    padding: 1.5rem;
    min-width: 280px;
    max-width: calc(100vw - 2rem);
  }
  
  .loading-header h4 {
    font-size: 1.1rem;
  }
  
  .layer-name {
    font-size: 0.9rem;
  }
  
  .layer-percentage {
    font-size: 0.85rem;
  }
}

/* Enhanced visual feedback */
.loading-overlay {
  animation: overlay-fade-in 0.3s ease-out;
}

@keyframes overlay-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.loading-content {
  animation: content-slide-in 0.4s ease-out 0.1s both;
}

@keyframes content-slide-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
