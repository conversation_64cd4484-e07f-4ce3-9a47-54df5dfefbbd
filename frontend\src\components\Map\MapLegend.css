
/* Highlighted Layer Name */
.highlighted-layer-name {
  color: #0d6efd !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(13, 110, 253, 0.2);
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.1), rgba(13, 110, 253, 0.05));
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #0d6efd;
}

/* Map Legend Panel */
.map-legend-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: white !important;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #ddd;
}

/* Minimized state */
.map-legend-panel.minimized {
  width: auto;
  height: auto;
  padding: 0;
}

/* Expanded state - match right-side legend panel width */
.map-legend-panel.expanded {
  width: 400px;
  min-height: 200px;
  max-height: 600px;
  padding: 0;
  overflow: hidden;
}

/* Legend Tab (Minimized State) */
.legend-tab {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.legend-tab:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

/* Legend Header (Expanded State) */
.legend-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid rgba(221, 221, 221, 0.3);
  background: transparent;
}

.legend-header-content {
  display: flex;
  align-items: center;
}

.legend-minimize-btn {
  padding: 6px 8px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transition: all 0.2s ease;
}

.legend-minimize-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.05);
}

.map-legend-panel .legend-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px 20px 25px 20px; /* Extra bottom padding for better scrolling */
  overflow-y: auto;
  max-height: 500px;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
.map-legend-panel .legend-content::-webkit-scrollbar {
  width: 6px;
}

.map-legend-panel .legend-content::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.map-legend-panel .legend-content::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.map-legend-panel .legend-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Scroll indicator for better UX */
.map-legend-panel.expanded::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.9));
  pointer-events: none;
  z-index: 1;
}

.map-legend-panel .legend-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0;
  color: #333;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.map-legend-panel .legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.map-legend-panel .legend-color {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-radius: 4px;
}

.map-legend-panel .legend-label {
  font-size: 14px;
  color: #333;
}

/* Legend Image Wrapper and Expand Button */
.legend-image-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.legend-image-wrapper {
  position: relative;
  display: inline-block;
  width: auto !important;
  max-width: 100%;
  flex-shrink: 0;
}

.clickable-legend {
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
  width: auto !important;
  max-width: 100% !important;
  height: auto !important;
  object-fit: contain !important;
  border-radius: 4px;
  border: 1px solid rgba(221, 221, 221, 0.5);
  background: rgba(255, 255, 255, 0.8);
  padding: 3px;
  display: block;
}

.clickable-legend:hover {
  transform: scale(1.02);
  opacity: 0.9;
}

.legend-expand-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(30, 58, 95, 0.9);
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.8;
}

.legend-expand-btn:hover {
  background: rgba(30, 58, 95, 1);
  opacity: 1;
  transform: scale(1.1);
}

/* Expanded Legend Panel Container - positioned on left of screen */
.expanded-legend-panel-container-left {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 2000;
  animation: panelSlideIn 0.3s ease-out;
}

/* Keep the old right container for backward compatibility */
.expanded-legend-panel-container-right {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 2000;
  animation: panelSlideIn 0.3s ease-out;
}

.expanded-legend-panel {
  background: white !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.9);
  width: 400px;
  max-width: 90vw;
  max-height: 70vh;
  overflow: hidden;
}

.expanded-legend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(221, 221, 221, 0.6);
  background: transparent;
}

/* Override for blue header styling - match sidebar blue */
.expanded-legend-header.app-header-blue,
.legend-header.app-header-blue {
  background: linear-gradient(135deg, #1e4080 0%, #0f2d5c 100%) !important;
  color: #ffffff !important;
  border-bottom: 1px solid #0f2d5c !important;
}

.expanded-legend-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.expanded-legend-close {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.expanded-legend-close:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.expanded-legend-content {
  padding: 20px;
  text-align: center;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.legend-content-layout {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  text-align: left;
}

.original-legend-container {
  flex: 1;
  min-width: 0;
  text-align: center;
}

.expanded-legend-image {
  max-width: 100%;
  max-height: calc(90vh - 120px);
  height: auto;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .map-legend {
    bottom: 10px;
    left: 10px;
    min-width: 150px;
    padding: 10px;
  }

  .map-legend-panel {
    width: 95vw;
    max-height: 60vh;
  }

  .map-legend-panel.expanded {
    width: 95vw;
    max-height: 60vh;
  }

  .expanded-legend-panel-container-right {
    bottom: 10px;
    right: 10px;
    left: 10px;
  }

  .expanded-legend-panel {
    width: 100%;
    max-width: none;
  }

  .expanded-legend-header {
    padding: 12px 16px;
  }

  .expanded-legend-title {
    font-size: 14px;
  }

  .expanded-legend-content {
    padding: 16px;
  }
}

/* Opacity Control Styles */
.opacity-control {
  padding: 8px 0;
  border-top: 1px solid rgba(221, 221, 221, 0.3);
}

.opacity-label-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.opacity-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin: 0;
}

.opacity-slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: linear-gradient(to right, rgba(13, 110, 253, 0.2), rgba(13, 110, 253, 0.8));
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.opacity-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.opacity-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.opacity-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.opacity-slider:focus {
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* Enhanced Legend Item Cards - optimized for scrolling */
.legend-item-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  margin-bottom: 18px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 140px;
  max-width: 100%;
  flex-shrink: 0;
}

.legend-item-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #0d6efd;
}

.legend-item-card.selected {
  border-color: #0d6efd;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  box-shadow: 0 4px 20px rgba(13, 110, 253, 0.15);
  transform: translateY(-1px);
}

.legend-item-card.selected:hover {
  box-shadow: 0 6px 24px rgba(13, 110, 253, 0.2);
  transform: translateY(-3px);
}

.legend-item-card:last-child {
  margin-bottom: 16px; /* Keep margin for better scrolling */
}

/* Color-coded accent bar */
.layer-accent-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 0 2px 2px 0;
  transition: all 0.3s ease;
}

.layer-accent-bar.selected {
  width: 6px;
  background: linear-gradient(135deg, #0d6efd, #0a58ca);
  box-shadow: 0 0 10px rgba(13, 110, 253, 0.3);
}

/* Layer Header Improvements */
.legend-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.layer-title-section {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
}

.layer-icon {
  color: #6c757d;
  margin-top: 2px;
  flex-shrink: 0;
}

.layer-title-container {
  flex: 1;
  min-width: 0;
}

.layer-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 4px 0;
  line-height: 1.3;
  cursor: help;
}

.layer-badges {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.layer-badge {
  font-size: 9px;
  padding: 2px 6px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.info-btn {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.info-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Enhanced Opacity Control */
.opacity-control-enhanced {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  border: 1px solid #e9ecef;
}

.opacity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.opacity-label {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0;
}

.opacity-value {
  font-size: 12px;
  font-weight: 700;
  color: #007bff;
  background: white;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #007bff;
  cursor: help;
}

.opacity-slider-container {
  position: relative;
}

.opacity-slider-enhanced {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, rgba(13, 110, 253, 0.2), rgba(13, 110, 253, 0.8));
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.opacity-slider-enhanced::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
  border: 3px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.opacity-slider-enhanced::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.opacity-marks {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 10px;
  color: #6c757d;
}

/* Enhanced Action Buttons */
.action-buttons-container {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  transform: translateY(0);
}

.metadata-btn:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-color: #0056b3;
  color: white;
}

.features-btn:hover {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  border-color: #1e7e34;
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn span {
  font-size: 11px;
}

/* Enhanced Legend Image Section */
.legend-image-section {
  margin: 12px 0;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

.legend-image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.legend-section-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.legend-expand-btn-enhanced {
  background: #007bff;
  border: none;
  border-radius: 6px;
  padding: 4px 6px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-expand-btn-enhanced:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.legend-image-wrapper-enhanced {
  background: white;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-image-enhanced {
  max-width: 100%;
  max-height: 150px;
  height: auto;
  width: auto;
  object-fit: contain;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.legend-image-enhanced:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.legend-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  text-align: center;
}

.fallback-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.7;
}

.fallback-text {
  font-size: 12px;
  font-weight: 500;
}

/* Responsive adjustments for legend layout */
@media (max-width: 768px) {
  .legend-content-layout {
    flex-direction: column;
    gap: 12px;
  }

  .expanded-legend-panel {
    width: 350px;
  }

  .map-legend-panel.expanded {
    width: 350px;
    max-height: 450px;
  }

  .legend-item-card {
    padding: 12px;
    margin-bottom: 12px;
  }

  .layer-title {
    font-size: 13px;
  }

  .action-buttons-container {
    flex-direction: column;
    gap: 6px;
  }

  .action-btn {
    padding: 6px 10px;
  }

  .opacity-control-enhanced {
    padding: 10px;
  }
}

/* ===== LEAFLET LEGEND CONTROL STYLES ===== */

/* Leaflet control specific styles */
.leaflet-legend-control {
  background: white !important;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #ddd;
  max-width: 500px;
  min-width: 400px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scrollable state indicator */
.leaflet-legend-control.scrollable::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.9));
  pointer-events: none;
  z-index: 1;
  border-radius: 0 0 8px 8px;
}

/* Ensure proper z-index for Leaflet controls */
.leaflet-control-container .leaflet-legend-control {
  z-index: 10000;
}

/* Reduce prominence of Leaflet attribution when legend is present */
.leaflet-control-attribution {
  opacity: 0.6;
  font-size: 10px;
  z-index: 100;
  bottom: 10px !important;
  right: 10px !important;
  max-width: 200px;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 3px;
  padding: 2px 5px;
}

.leaflet-control-attribution:hover {
  opacity: 1;
}

/* Hide attribution when legend has many items to prevent overlap */
.leaflet-container:has(.leaflet-legend-control.scrollable) .leaflet-control-attribution {
  display: none;
}

/* Position adjustments for left side placement */
.leaflet-left .leaflet-legend-control {
  margin-left: 10px;
  margin-bottom: 40px; /* Increased margin to avoid attribution overlap */
}

/* Alternative: Move attribution when legend is present */
.leaflet-container .leaflet-control-attribution {
  transition: all 0.3s ease;
}

/* When legend control is present, move attribution to top-right */
.leaflet-container:has(.leaflet-legend-control) .leaflet-control-attribution {
  bottom: auto !important;
  top: 10px !important;
  right: 10px !important;
  left: auto !important;
}

/* Fallback class-based approach for better browser support */
.leaflet-container.legend-active .leaflet-control-attribution {
  bottom: auto !important;
  top: 10px !important;
  right: 10px !important;
  left: auto !important;
  font-size: 9px;
  max-width: 150px;
}

/* Override leaflet control bar styles for legend */
.leaflet-legend-control.leaflet-bar {
  border: 1px solid rgba(221, 221, 221, 0.8);
  border-radius: 8px;
}

/* Ensure legend control doesn't interfere with map interaction */
.leaflet-legend-control .legend-content {
  pointer-events: auto;
}

/* Responsive adjustments for Leaflet legend control */
@media (max-width: 768px) {
  .leaflet-legend-control {
    max-width: 95vw;
    min-width: 320px;
  }

  .leaflet-legend-control .legend-content {
    max-height: 60vh;
  }
}

/* Enhanced Legend Image Styles for Leaflet Control */
.legend-image-wrapper-enhanced {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px 0;
  min-height: 60px;
}

.legend-image-enhanced {
  max-width: 100%;
  max-height: 120px;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid rgba(221, 221, 221, 0.5);
  background: rgba(255, 255, 255, 0.8);
  padding: 4px;
  transition: all 0.2s ease;
}

.legend-image-enhanced:hover {
  transform: scale(1.02);
  border-color: #0d6efd;
}

.legend-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dee2e6;
}

.fallback-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.fallback-text {
  font-size: 12px;
  text-align: center;
}

/* Enhanced Opacity Control for Leaflet Legend */
.opacity-control-enhanced {
  margin-top: 12px;
  padding: 12px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(221, 221, 221, 0.3);
}

.opacity-control-enhanced .opacity-label-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.opacity-control-enhanced .opacity-label {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.opacity-control-enhanced .opacity-value {
  font-size: 11px;
  color: #6c757d;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(221, 221, 221, 0.5);
}

.opacity-control-enhanced .opacity-slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.opacity-control-enhanced .opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.opacity-control-enhanced .opacity-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.opacity-control-enhanced .opacity-slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

/* Badge Styles for Leaflet Legend */
.layer-info-badge {
  background-color: #0d6efd !important;
  font-size: 10px;
  padding: 3px 8px;
  cursor: help;
  transition: all 0.2s ease;
}

.layer-info-badge:hover {
  background-color: #0b5ed7 !important;
  transform: scale(1.05);
}

.layer-info-badge i {
  margin-right: 3px;
}

/* Enhanced Legend Panel Styles */

/* User mode toggle styles */
.user-mode-toggle .mode-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.user-mode-toggle .mode-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.legend-header-actions {
  display: flex;
  align-items: center;
}

/* Enhanced action buttons */
.action-buttons-container {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  min-width: 80px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.action-btn span {
  margin-left: 0.25rem;
}

/* Help content styles */
.help-content {
  max-height: 500px;
  overflow-y: auto;
}

.help-content h6 {
  color: #0d6efd;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.25rem;
  margin-bottom: 0.75rem;
}

.help-content ul {
  padding-left: 1.25rem;
}

.help-content li {
  margin-bottom: 0.5rem;
}

/* Enhanced layer badges */
.layer-badges {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.layer-badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Selected layer badge */
.selected-badge {
  font-size: 0.6rem;
  padding: 0.2rem 0.4rem;
  border-radius: 0.2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Layer description for simple mode */
.layer-description {
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border-left: 3px solid #0d6efd;
}

.layer-description small {
  line-height: 1.4;
  font-size: 0.8rem;
}

/* Simple mode specific styles */
.user-mode-simple .layer-badge {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border: 1px solid #bbdefb;
}

.user-mode-simple .action-btn {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

.user-mode-simple .action-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

/* Advanced mode specific styles */
.user-mode-advanced .layer-badge {
  font-family: 'Courier New', monospace;
}

.user-mode-advanced .action-btn {
  font-size: 0.7rem;
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .user-mode-toggle {
    display: none; /* Hide mode toggle on mobile */
  }

  .action-buttons-container {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn {
    min-width: 100%;
    justify-content: center;
  }
}