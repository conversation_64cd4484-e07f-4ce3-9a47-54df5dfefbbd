import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON>, Spinner, Badge } from 'react-bootstrap';
import { ChevronDown, ChevronRight, Database, Satellite, Waves, BarChart3, CloudRain, Users, MapPin, Settings } from 'lucide-react';
import { LayerDiscovery } from '../../types/discovery';
import { discoverLayers, CategoryInfo, CategorizedLayers } from '../../services/discoveryService';
import { fetchLayerStyles } from '../../services/geoserverService';
import LayerDetailsModal from '../LayerDetails/LayerDetailsModal';
import './DataLayers.css';

interface DataLayersProps {
  layers: LayerDiscovery[];
  selectedLayerNames: string[];
  onLayerChange: (layerName: string) => void;
  isLoading?: boolean;
  error?: string | null;
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Layer information control
  onShowLayerInfo?: (layerName: string) => void;
  // Layer opacity control
  layerOpacities?: { [layerName: string]: number };
  onOpacityChange?: (layerName: string, opacity: number) => void;
}

// Dynamic accordion sections - will be populated from discovery

// Function to get appropriate icon for category based on name/keywords
const getCategoryIcon = (categoryName: string) => {
  const name = categoryName.toLowerCase();

  if (name.includes('satellite') || name.includes('sentinel') || name.includes('landsat') || name.includes('modis')) {
    return Satellite;
  } else if (name.includes('flood') || name.includes('water') || name.includes('hydro')) {
    return Waves;
  } else if (name.includes('historical') || name.includes('history') || name.includes('archive')) {
    return BarChart3;
  } else if (name.includes('climate') || name.includes('weather') || name.includes('meteorology') || name.includes('precipitation')) {
    return CloudRain;
  } else if (name.includes('human') || name.includes('settlement') || name.includes('population') || name.includes('demographic')) {
    return Users;
  } else if (name.includes('service') || name.includes('point') || name.includes('infrastructure')) {
    return MapPin;
  } else if (name.includes('admin') || name.includes('boundary') || name.includes('administrative')) {
    return Settings;
  } else {
    // Default icon for unknown categories
    return Database;
  }
};

const DataLayers: React.FC<DataLayersProps> = ({
  layers,
  selectedLayerNames,
  onLayerChange,
  isLoading = false,
  error = null,
  selectedBasemap = 'osm:osm', // Default to OpenStreetMap
  onBasemapChange,
  onShowLayerInfo,
  layerOpacities = {},
  onOpacityChange
}) => {

  // Helper function to render layer items with info badges
  const renderLayerItem = (layer: LayerDiscovery, categoryKey: string) => {
    return (
      <div key={layer.name} className="layer-item-with-info">
        <div className="layer-checkbox-container">
          <Form.Check
            type="checkbox"
            id={`${categoryKey}-layer-${layer.name}`}
            checked={selectedLayerNames.includes(layer.name)}
            onChange={() => onLayerChange(layer.name)}
            className="layer-checkbox"
            style={{
              fontSize: '0.8rem',
              marginBottom: '0.3rem'
            }}
          />
          <label
            htmlFor={`${categoryKey}-layer-${layer.name}`}
            className="layer-label-with-info"
            style={{ fontSize: '0.8rem', marginLeft: '0.5rem', cursor: 'pointer' }}
          >
            {layer.title || layer.name}
            {(layer as any).isRemote && (
              <Badge
                bg="warning"
                className="ms-1"
                style={{ fontSize: '0.6rem' }}
                title="Remote/External Layer"
              >
                Remote
              </Badge>
            )}
            {/* Layer options dropdown removed as requested */}
            {/* {selectedLayerNames.includes(layer.name) && (
              <Dropdown drop="down" align="start">
                <Dropdown.Toggle
                  variant="secondary"
                  size="sm"
                  className="layer-options-toggle"
                  style={{
                    fontSize: '0.6rem',
                    marginLeft: '0.5rem',
                    padding: '0.15rem',
                    width: '16px',
                    height: '16px',
                    borderRadius: '3px',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none'
                  }}
                  title="Layer options"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <ArrowDownRight size={8} />
                </Dropdown.Toggle>

                <Dropdown.Menu className="layer-options-menu">
                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShowLayerDetails(layer);
                    }}
                  >
                    <Info size={12} className="me-2" />
                    Layer Details
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(layer.name, { format: 'geojson' });
                    }}
                  >
                    <Download size={12} className="me-2" />
                    Download
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      // Show layer info in legend panel as fallback
                      onShowLayerInfo?.(layer.name);
                    }}
                  >
                    <Eye size={12} className="me-2" />
                    Show in Legend
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('🕐 Temporal controls for:', layer.name);
                      console.log('📊 Layer metadata:', layer);
                      // Add temporal controls
                    }}
                  >
                    <Calendar size={12} className="me-2" />
                    Time Controls
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      testLayerStyles(layer);
                    }}
                  >
                    <Palette size={12} className="me-2" />
                    Test Styles
                  </Dropdown.Item>

                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Data source:', layer.name);
                      // Add data source info
                    }}
                  >
                    <Database size={12} className="me-2" />
                    Data Source
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            )} */}

            {/* Temporary test button for debugging */}
            {selectedLayerNames.includes(layer.name) && (
              <button
                onClick={() => {
                  handleShowLayerDetails(layer);
                }}
                style={{
                  marginLeft: '0.5rem',
                  padding: '2px 6px',
                  fontSize: '0.7rem',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px'
                }}
              >
                Info
              </button>
            )}
          </label>
        </div>

        {/* Opacity Control for Selected Layers - Commented out as requested */}
        {/* {selectedLayerNames.includes(layer.name) && onOpacityChange && (
          <div className="layer-opacity-control" style={{
            marginTop: '0.5rem',
            marginLeft: '1.5rem',
            marginRight: '0.5rem'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '0.25rem'
            }}>
              <label style={{
                fontSize: '0.7rem',
                color: '#6c757d',
                margin: 0
              }}>
                Opacity
              </label>
              <span style={{
                fontSize: '0.7rem',
                fontWeight: 'bold',
                color: '#007bff'
              }}>
                {Math.round((layerOpacities[layer.name] || 1.0) * 100)}%
              </span>
            </div>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={layerOpacities[layer.name] || 1.0}
              onChange={(e) => onOpacityChange(layer.name, parseFloat(e.target.value))}
              style={{
                width: '100%',
                height: '4px',
                borderRadius: '2px',
                background: 'linear-gradient(to right, rgba(13, 110, 253, 0.2), rgba(13, 110, 253, 0.8))',
                outline: 'none',
                cursor: 'pointer'
              }}
            />
          </div>
        )} */}
      </div>
    );
  };

  const [openSections, setOpenSections] = useState<string[]>([]); // All collapsed by default

  // Dynamic discovery state
  const [discoveryCategories, setDiscoveryCategories] = useState<CategoryInfo[]>([]);
  const [discoveryLayers, setDiscoveryLayers] = useState<LayerDiscovery[]>([]);
  const [discoveryCategorized, setDiscoveryCategorized] = useState<CategorizedLayers>({});
  const [discoveryLoading, setDiscoveryLoading] = useState(false);
  const [discoveryError, setDiscoveryError] = useState<string | null>(null);

  // Layer details modal state
  const [showLayerDetailsModal, setShowLayerDetailsModal] = useState(false);
  const [selectedLayerForDetails, setSelectedLayerForDetails] = useState<LayerDiscovery | null>(null);

  // Perform discovery on component mount
  useEffect(() => {
    const performDiscovery = async () => {
      setDiscoveryLoading(true);
      setDiscoveryError(null);

      try {
        const discovery = await discoverLayers();

        setDiscoveryCategories(discovery.categories);
        setDiscoveryLayers(discovery.layers);
        setDiscoveryCategorized(discovery.categorized);

      } catch (error: any) {
        console.error('DataLayers: Discovery failed:', error);
        setDiscoveryError(error.message);
      } finally {
        setDiscoveryLoading(false);
      }
    };

    performDiscovery();
  }, []); // Run once on mount

  // Use discovery categorized layers only - no fallback to old categorization
  const categorizedLayers: CategorizedLayers = useMemo(() => {
    if (Object.keys(discoveryCategorized).length > 0) {
      return discoveryCategorized;
    } else {
      // Return empty categorization until discovery completes
      return {};
    }
  }, [discoveryCategorized]);

  // Function to get layer count for each category (discovery only)
  const getLayerCount = (categoryKey: string) => {
    return categorizedLayers[categoryKey]?.length || 0;
  };

  const toggleSection = (key: string) => {
    setOpenSections((prev: string[]) =>
      prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]
    );
  };

  // Handler functions for layer details modal
  const handleShowLayerDetails = (layer: LayerDiscovery) => {
    console.log('Opening layer details for:', layer.name);
    setSelectedLayerForDetails(layer);
    setShowLayerDetailsModal(true);
  };

  const handleCloseLayerDetails = () => {
    setShowLayerDetailsModal(false);
    setSelectedLayerForDetails(null);
  };

  const handleStyleChange = (layerName: string, styleName: string) => {
    console.log(`Style changed for layer ${layerName}: ${styleName}`);
    // TODO: Implement style change functionality
  };

  const handleDownload = (layerName: string, options: any) => {
    console.log(`Download requested for layer ${layerName}:`, options);
    // TODO: Implement download functionality
  };

  // Test styles functionality
  const testLayerStyles = async (layer: LayerDiscovery) => {
    console.log(`🎨 Testing styles for layer: ${layer.name}`);
    console.log('📊 Layer data:', layer);
    
    try {
      // Test the styles API endpoint
      const stylesData = await fetchLayerStyles(layer.name);
      console.log('✅ Styles API Response:', stylesData);
      
      if (stylesData && stylesData.styles) {
        console.log(`🎨 Found ${stylesData.styles.length} styles for ${layer.name}:`);
        stylesData.styles.forEach((style: any, index: number) => {
          console.log(`  ${index + 1}. ${style.name} ${style.isDefault ? '(default)' : ''}`);
          console.log(`     Title: ${style.title || 'No title'}`);
          console.log(`     Legend URL: ${style.legendUrl || 'No legend URL'}`);
        });
        
        // Show an alert with the results
        alert(`✅ Styles working! Found ${stylesData.styles.length} styles for "${layer.title || layer.name}". Check browser console for details.`);
      } else {
        console.warn('⚠️ No styles found in response:', stylesData);
        alert(`⚠️ No styles found for "${layer.title || layer.name}". This might be normal for some layers.`);
      }
    } catch (error) {
      console.error('❌ Error testing styles:', error);
      alert(`❌ Error testing styles for "${layer.title || layer.name}": ${error}`);
    }
  };

  return (
    <div className="data-layers section">
      {isLoading && (
        <div className="section-loading mb-3">
          <Spinner animation="border" size="sm" className="me-2" />
          <small className="text-muted">Discovering layers...</small>
        </div>
      )}
      {error && (
        <div className="section-error mb-3">
          <small className="text-danger">{error}</small>
        </div>
      )}
      {/* Show discovery loading state */}
      {discoveryLoading && (
        <div className="text-center py-3">
          <Spinner animation="border" size="sm" className="me-2" />
          <small className="text-muted">Discovering layers...</small>
        </div>
      )}

      {/* Show discovery error */}
      {discoveryError && (
        <div className="text-center py-2">
          <small className="text-danger">Discovery failed: {discoveryError}</small>
        </div>
      )}

      {/* Show message when no categories discovered yet */}
      {!discoveryLoading && !discoveryError && discoveryCategories.length === 0 && (
        <div className="text-center py-3">
          <small className="text-muted">No layer categories discovered yet</small>
        </div>
      )}

      {/* Render dynamic accordion sections based on discovered categories only */}
      {discoveryCategories.length > 0 && discoveryCategories.map(({ key, title, description, count }) => (
        <div key={key} className="custom-accordion-section">
          <div
            className="custom-accordion-header data-layer-accordion-header"
            onClick={() => toggleSection(key)}
            style={{
              cursor: 'pointer',
              fontWeight: 'bold',
              fontSize: '0.9rem',
              background: 'rgba(255, 255, 255, 0.2)', // Sidebar blue with semi-transparent white overlay
              color: 'white',
              border: 'none',
              borderRadius: '0',
              padding: '0.4rem 0.8rem',
              marginBottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center', // Center align the content
              position: 'relative', // For absolute positioning of icon and arrow
              transition: 'background-color 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            }}
          >
            <div style={{ position: 'absolute', left: '0.8rem', display: 'flex', alignItems: 'center' }}>
              {React.createElement(getCategoryIcon(title), { size: 14 })}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
              <span>{title}</span>
            </div>
            <div style={{ position: 'absolute', right: '0.8rem', display: 'flex', alignItems: 'center' }}>
              {openSections.includes(key) ? (
                <ChevronDown size={14} />
              ) : (
                <ChevronRight size={14} />
              )}
            </div>
          </div>
          {openSections.includes(key) && (
            <div
              className="custom-accordion-body"
              style={{
                border: 'none',
                borderTop: 'none',
                borderRadius: '0',
                padding: '0.6rem 0.8rem',
                marginBottom: '0.8rem',
                background: 'rgba(255, 255, 255, 0.05)',
                fontSize: '0.85rem'
              }}
            >
              {/* Dynamic content based on category key */}
              {key === 'basemaps' ? (
                <Form>
                  {/* Dynamic basemap layers from discovery */}
                  {categorizedLayers.basemaps && categorizedLayers.basemaps.length > 0 ? (
                    categorizedLayers.basemaps.map(layer => (
                      <Form.Check
                        key={layer.name}
                        type="radio"
                        name="basemap-selection"
                        id={`basemap-layer-${layer.name}`}
                        label={layer.title || layer.name}
                        checked={selectedBasemap === layer.name}
                        onChange={() => onBasemapChange?.(layer.name)}
                        className="layer-checkbox"
                        style={{
                          fontSize: '0.8rem',
                          marginBottom: '0.3rem'
                        }}
                      />
                    ))
                  ) : (
                    <div className="text-muted text-center py-2">
                      <small>No basemap layers available from discovery</small>
                    </div>
                  )}
                  {/* Always include OpenStreetMap as fallback */}
                  <Form.Check
                    key="osm"
                    type="radio"
                    name="basemap-selection"
                    id="basemap-osm"
                    label="OpenStreetMap"
                    checked={selectedBasemap === 'osm:osm'}
                    onChange={() => onBasemapChange?.('osm:osm')}
                    className="layer-checkbox"
                    style={{
                      fontSize: '0.8rem',
                      marginBottom: '0.3rem'
                    }}
                  />
                  <div className="mt-2">
                    <small className="text-muted">
                      Basemaps are discovered dynamically from GeoServer
                    </small>
                  </div>
                </Form>

              ) : (
                <div>
                  {/* Generic dynamic content for all other categories */}
                  {categorizedLayers[key] && categorizedLayers[key].length > 0 ? (
                    categorizedLayers[key].map((layer: LayerDiscovery) => renderLayerItem(layer, key))
                  ) : (
                    <div className="text-muted text-center py-2">
                      <small>No {title.toLowerCase()} available</small>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      ))}

      {/* Layer Details Modal */}
      <LayerDetailsModal
        show={showLayerDetailsModal}
        onHide={handleCloseLayerDetails}
        layer={selectedLayerForDetails}
        onStyleChange={handleStyleChange}
        onDownload={handleDownload}
      />
    </div>
  );
};

export default DataLayers;