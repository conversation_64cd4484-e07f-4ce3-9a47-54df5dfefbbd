.map-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: white;
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.legend-title {
  font-size: 16px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 8px;
  text-align: center;
}

.legend-items {
  display: flex;
  flex-direction: column;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 1px solid #ddd;
}

.legend-label {
  font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .map-legend {
    bottom: 10px;
    right: 10px;
    padding: 8px;
  }
  
  .legend-title {
    font-size: 14px;
  }
  
  .legend-color {
    width: 16px;
    height: 16px;
  }
  
  .legend-label {
    font-size: 12px;
  }
}