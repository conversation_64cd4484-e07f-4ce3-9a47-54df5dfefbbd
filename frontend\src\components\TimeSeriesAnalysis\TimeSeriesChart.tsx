import React from 'react';
import { Card } from 'react-bootstrap';

interface TimeSeriesChartProps {
  data: {
    chartType: 'line' | 'bar';
    chartData: {
      labels: string[];
      datasets: Array<{
        label: string;
        data: number[];
        borderColor?: string;
        backgroundColor?: string | ((context: any) => string);
        tension?: number;
      }>;
    };
    chartOptions?: any;
  };
  title?: string;
  height?: number;
}

const TimeSeriesChart: React.FC<TimeSeriesChartProps> = ({
  data,
  title,
  height = 300
}) => {
  const { chartData, chartType } = data;

  // Simple SVG-based chart implementation
  const renderLineChart = () => {
    if (!chartData.datasets[0] || chartData.datasets[0].data.length === 0) {
      return <div className="text-muted text-center p-4">No data available</div>;
    }

    const dataset = chartData.datasets[0];
    const values = dataset.data;
    const labels = chartData.labels;
    
    const maxValue = Math.max(...values);
    const minValue = Math.min(...values);
    const range = maxValue - minValue || 1;
    
    const width = 600;
    const chartHeight = height - 80; // Leave space for labels
    const padding = 40;
    
    // Calculate points for the line
    const points = values.map((value, index) => {
      const x = padding + (index / (values.length - 1)) * (width - 2 * padding);
      const y = padding + ((maxValue - value) / range) * (chartHeight - 2 * padding);
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="text-center">
        <svg width={width} height={height} className="border rounded">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => {
            const y = padding + ratio * (chartHeight - 2 * padding);
            return (
              <g key={i}>
                <line
                  x1={padding}
                  y1={y}
                  x2={width - padding}
                  y2={y}
                  stroke="#e0e0e0"
                  strokeWidth="1"
                />
                <text
                  x={padding - 10}
                  y={y + 4}
                  fontSize="10"
                  fill="#666"
                  textAnchor="end"
                >
                  {(maxValue - ratio * range).toFixed(2)}
                </text>
              </g>
            );
          })}
          
          {/* Data line */}
          <polyline
            points={points}
            fill="none"
            stroke={dataset.borderColor || '#007bff'}
            strokeWidth="2"
          />
          
          {/* Data points */}
          {values.map((value, index) => {
            const x = padding + (index / (values.length - 1)) * (width - 2 * padding);
            const y = padding + ((maxValue - value) / range) * (chartHeight - 2 * padding);
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="3"
                fill={dataset.borderColor || '#007bff'}
              />
            );
          })}
          
          {/* X-axis labels */}
          {labels.map((label, index) => {
            const x = padding + (index / (labels.length - 1)) * (width - 2 * padding);
            return (
              <text
                key={index}
                x={x}
                y={height - 10}
                fontSize="10"
                fill="#666"
                textAnchor="middle"
              >
                {label}
              </text>
            );
          })}
        </svg>
      </div>
    );
  };

  const renderBarChart = () => {
    if (!chartData.datasets[0] || chartData.datasets[0].data.length === 0) {
      return <div className="text-muted text-center p-4">No data available</div>;
    }

    const dataset = chartData.datasets[0];
    const values = dataset.data;
    const labels = chartData.labels;
    
    const maxValue = Math.max(...values, 0);
    const minValue = Math.min(...values, 0);
    const range = maxValue - minValue || 1;
    
    const width = 600;
    const chartHeight = height - 80;
    const padding = 40;
    const barWidth = (width - 2 * padding) / values.length * 0.8;
    const barSpacing = (width - 2 * padding) / values.length * 0.2;

    // Function to get bar color based on value (for VCI analysis)
    const getBarColor = (value: number) => {
      if (typeof dataset.backgroundColor === 'function') {
        return dataset.backgroundColor({ parsed: { y: value } });
      }
      return dataset.backgroundColor || '#007bff';
    };

    return (
      <div className="text-center">
        <svg width={width} height={height} className="border rounded">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => {
            const y = padding + ratio * (chartHeight - 2 * padding);
            return (
              <g key={i}>
                <line
                  x1={padding}
                  y1={y}
                  x2={width - padding}
                  y2={y}
                  stroke="#e0e0e0"
                  strokeWidth="1"
                />
                <text
                  x={padding - 10}
                  y={y + 4}
                  fontSize="10"
                  fill="#666"
                  textAnchor="end"
                >
                  {(maxValue - ratio * range).toFixed(0)}
                </text>
              </g>
            );
          })}
          
          {/* Bars */}
          {values.map((value, index) => {
            const x = padding + index * (width - 2 * padding) / values.length + barSpacing / 2;
            const barHeight = Math.abs(value - minValue) / range * (chartHeight - 2 * padding);
            const y = padding + (chartHeight - 2 * padding) - barHeight;
            
            return (
              <rect
                key={index}
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={getBarColor(value)}
                stroke="none"
              />
            );
          })}
          
          {/* X-axis labels */}
          {labels.map((label, index) => {
            const x = padding + index * (width - 2 * padding) / labels.length + (width - 2 * padding) / labels.length / 2;
            return (
              <text
                key={index}
                x={x}
                y={height - 10}
                fontSize="10"
                fill="#666"
                textAnchor="middle"
              >
                {label}
              </text>
            );
          })}
        </svg>
      </div>
    );
  };

  return (
    <Card>
      {title && (
        <Card.Header>
          <h6 className="mb-0">{title}</h6>
        </Card.Header>
      )}
      <Card.Body>
        {chartType === 'line' ? renderLineChart() : renderBarChart()}
        
        {/* Legend */}
        {chartData.datasets.length > 0 && (
          <div className="mt-3 text-center">
            <div className="d-flex justify-content-center align-items-center">
              <div 
                className="me-2"
                style={{
                  width: '12px',
                  height: '12px',
                  backgroundColor: chartData.datasets[0].borderColor || chartData.datasets[0].backgroundColor || '#007bff',
                  borderRadius: chartType === 'line' ? '50%' : '2px'
                }}
              />
              <small className="text-muted">{chartData.datasets[0].label}</small>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default TimeSeriesChart;
