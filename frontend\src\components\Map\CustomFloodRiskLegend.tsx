import React from 'react';
import './CustomFloodRiskLegend.css';

interface CustomFloodRiskLegendProps {
  className?: string;
}

const CustomFloodRiskLegend: React.FC<CustomFloodRiskLegendProps> = ({ className = '' }) => {
  const legendItems = [
    { color: '#dc3545', label: 'High' },      // Red
    { color: '#ffc107', label: 'Moderate' },  // Yellow
    { color: '#28a745', label: 'Low' },       // Green
    { color: '#90ee90', label: 'Very Low' },  // Light green
    { color: '#007bff', label: 'River' },     // Blue
  ];

  return (
    <div className={`custom-flood-risk-legend ${className}`}>
      <div className="custom-legend-header">
        <h4 className="custom-legend-title">FLOOD RISK</h4>
      </div>
      <div className="custom-legend-content">
        {legendItems.map((item, index) => (
          <div key={index} className="custom-legend-row">
            <div
              className="custom-legend-color-box"
              style={{ backgroundColor: item.color }}
            />
            <span className="custom-legend-label">{item.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CustomFloodRiskLegend;
