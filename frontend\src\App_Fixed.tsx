import { useState, useEffect } from 'react';
import { Container } from 'react-bootstrap';
import NavBar from './components/NavBar/NavBar';
import Sidebar from './components/Sidebar/Sidebar';
import MapComponent from './components/Map/MapComponent';
import AnalyticsDashboard from './components/Analytics/AnalyticsDashboard';
import { useDiscoveryLayers } from './hooks/useDiscoveryLayers';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  const { layers, selectedLayers, handleLayerToggle, isLoading, error } = useDiscoveryLayers();

  const [currentView, setCurrentView] = useState<'map' | 'analytics'>('map');

  const [dateRange, setDateRange] = useState({
    startDate: '2025/05/20',
    endDate: '2025/05/20'
  });
  const [drawnItems, setDrawnItems] = useState<any>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleDateChange = (type: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({ ...prev, [type]: value }));
  };

  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
  };

  const handlePreviewData = () => {
    alert(`🔍 Data Preview Requested!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Previewing data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleDownloadData = () => {
    console.log('Downloading data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleQueryTemporalData = () => {
    alert(`🔍 Temporal Query Executed!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Temporal layers selected:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  useEffect(() => {
    const checkSidebarState = () => {
      const state = localStorage.getItem('sidebarCollapsed') === 'true';
      setSidebarCollapsed(state);
      console.log('Sidebar collapsed state loaded:', state);
    };

    checkSidebarState();
    
    const handleStorageChange = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <div className="app-wrapper">
      <NavBar onNavigate={setCurrentView} />
      {currentView === 'analytics' ? (
        <Container fluid className="app-container">
          <AnalyticsDashboard />
        </Container>
      ) : (
        <Container fluid className="app-container">
          <div 
            className="main-content" 
            style={{ 
              display: 'flex', 
              height: '100%', 
              width: '100%', 
              minHeight: 'calc(100vh - 60px)',
              position: 'relative'
            }}
          >
            <div 
              className="sidebar-container"
              style={{
                width: sidebarCollapsed ? 40 : 300,
                minWidth: sidebarCollapsed ? 40 : 300,
                maxWidth: sidebarCollapsed ? 40 : 300,
                transition: 'all 0.3s ease',
                height: '100%'
              }}
            >
              <Sidebar
                layers={layers}
                selectedLayerNames={selectedLayers}
                onLayerChange={handleLayerToggle}
                dateRange={dateRange}
                onDateChange={handleDateChange}
                onSearch={handleSearch}
                onPreviewData={handlePreviewData}
                onDownloadData={handleDownloadData}
                onQueryTemporalData={handleQueryTemporalData}
                isLoading={isLoading}
                error={error}
              />
            </div>
            <div className="map-container" style={{ flex: 1, width: '100%', height: '100%' }}>
              <MapComponent
                selectedLayerNames={selectedLayers}
                dateRange={dateRange}
                onDrawComplete={setDrawnItems}
              />
            </div>
          </div>
        </Container>
      )}
    </div>
  );
}

export default App;
