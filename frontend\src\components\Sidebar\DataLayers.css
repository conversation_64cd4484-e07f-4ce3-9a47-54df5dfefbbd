/* Layer Options Dropdown Styling */
.layer-options-toggle {
  transition: all 0.2s ease;
}

.layer-options-toggle:hover {
  background-color: #5a6268 !important;
  transform: scale(1.1);
}

.layer-options-menu {
  min-width: 160px;
  border-radius: 2px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 2px 0;
}

.layer-options-menu .dropdown-item {
  padding: 8px 12px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.layer-options-menu .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #495057;
  transform: translateX(2px);
}

.layer-options-menu .dropdown-item:active {
  background-color: #e9ecef;
}

/* Ensure dropdown doesn't interfere with layer selection */
.layer-label-with-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Custom dropdown toggle to prevent Bootstrap default styling */
.layer-options-toggle::after {
  display: none !important;
}

.layer-options-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Animation for dropdown appearance */
.layer-options-menu {
  animation: dropdownFadeIn 0.15s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Layer Opacity Control Styles - Simplified */
.layer-opacity-control {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 4px 0;
  transition: all 0.2s ease;
}

.layer-opacity-control:hover {
  background: transparent;
}

.layer-opacity-control input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, rgba(13, 110, 253, 0.3), rgba(13, 110, 253, 0.8));
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.layer-opacity-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.layer-opacity-control input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.layer-opacity-control input[type="range"]::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layer-options-menu {
    min-width: 140px;
  }

  .layer-options-menu .dropdown-item {
    padding: 6px 10px;
    font-size: 0.75rem;
  }

  .layer-opacity-control {
    padding: 6px;
    margin-top: 0.25rem;
  }
}
